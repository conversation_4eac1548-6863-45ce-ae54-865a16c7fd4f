import hashlib
import json
import logging
import queue
import re
import threading
import time

import pymysql
import requests
from flask import Flask, request, jsonify, render_template, redirect, Response, session, current_app, make_response

from flask_apscheduler import APScheduler

from dbutils.pooled_db import PooledDB
import sqlite3
from sqlite_config import *
from modules.op_sqlite import OpDevice, OpHistory, OpRecord, OpCrane
from modules.PLC import PLC

from global_config import _config

from modules.task_thread import ThreadPool
from modules.OA_360_spider import Spider360
# from modules.E_mobile_websocket import MyWebSocket

app = Flask(__name__)
appid = 'wxeb35104975a53783'
secret = '7a0854d211f4523fd346de1a0de8c39c'

app.secret_key = 'gsh1493829867.'
app.config['PREMANENT_SESSION_LIFETIME'] = 7200

# 实例化 APScheduler
scheduler = APScheduler()

TOKEN = 'gsh1493829867'

app.sqlite_pool = PooledDB(
    creator=sqlite3,
    database=SQLITE_DB_PATH,
    **POOL_CONFIG
)

# 配置日志记录
# logging.basicConfig(filename='./logs/error.log', level=logging.INFO,
#                     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 禁用某类日志输出
# logging.getLogger('geventwebsocket.handler').setLevel(logging.CRITICAL)
# logging.getLogger('apscheduler.scheduler').setLevel(logging.CRITICAL)

# 创建一个日志记录器
logger = logging.getLogger(__name__)


# @scheduler.task('cron', id='get_t_h', day='*', hour='*', minute='*')
# @scheduler.task('interval', id='main_check', seconds=10)
# def get_temperature_humidity():  # 运行的定时任务的函数
#     pool = ThreadPool(max_workers=5)
#     for i in _config['temperature_humidity_info_list']:
#         read_client = PLC(ip=i['ip'], port=i['port'])
#         pool.submit(read_client.read_registers, i['slave_address'], i['start_address'], i['num_registers'])
#     pool.wait_completion()
#     result_list = pool.shutdown()
#     print(f'result_list: {result_list}')


# @scheduler.task('interval', id='main_check', seconds=10)
# @scheduler.task('cron', id='main_check', day='*', hour='*', minute='*')
def main_check():  # 运行的定时任务的函数
    op = OpDevice()
    temp_list = op.get_department_from_devices_2_states('开启', '开启')
    department_list = []
    for i in temp_list:
        department_list.append(i[0])
    link_360 = Spider360()
    print(f"{'登录成功' if link_360.login('eppadmin', 'Humenjx@4801')['errno'] == 0 else '登录失败'}")
    get_online_info = link_360.get_gorp()
    op_history = OpHistory()
    for i in department_list:
        base_info = get_online_info[i]
        online_num = base_info['online_num']
        print(i, online_num, base_info)
        op_history.add_data(i, '开启', '开启', online_num)


# 开启定时任务
# scheduler.init_app(app)
# scheduler.start()

# print(sock.send_query('[&quot;623&quot;,&quot;157&quot;,&quot;367&quot;,&quot;98&quot;,&quot;425&quot;,&quot;95&quot;]'))

@app.route('/', methods=['GET'])
def redirect_():
    if request.method == 'GET':
        return redirect('/web/login')

@app.route('/getIp', methods=['GET'])
def get_ip():
    if request.method == 'GET':
        print(request.remote_addr)
        return jsonify({'ip': request.remote_addr})

@app.route('/test', methods=['GET'])
def test():
    if request.method == 'GET':
        return render_template('test.html')


from routes.web.web import web_html, format_info
from routes.web.notice import web_notice
from routes.web.search import web_search
from routes.web.opDevice import web_device
from routes.web.editTask import web_edit_task
from routes.web.proxy import web_proxy
from routes.web.door import web_door
from routes.web.aircondition import web_aircondition

app.register_blueprint(web_door, url_prefix='/door')
app.register_blueprint(web_proxy, url_prefix='/proxy')
app.register_blueprint(web_html, url_prefix='/web')
app.register_blueprint(web_notice, url_prefix='/notice')
app.register_blueprint(web_search, url_prefix='/search')
app.register_blueprint(web_device, url_prefix='/device')
app.register_blueprint(web_edit_task, url_prefix='/task')
app.register_blueprint(web_aircondition, url_prefix='/aircondition')
# print(app.jinja_loader.list_templates())





if __name__ == '__main__':
    # app.debug = True
    app.run(debug=True, port=50, host='0.0.0.0')
