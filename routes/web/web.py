# -*- coding:utf-8 -*-
import queue
import time

from flask import Blueprint, render_template, jsonify, request, session, redirect

from modules.E_mobile_websocket import MyWebSocket
from modules.OA_360_spider import Spider360
from modules.op_sqlite import OpRecord, OpDevice

from test import *
import math
from global_config import _config



admin = 'admin'
admin_pwd = 'admin'
web_html = Blueprint('web', __name__)

department_id = _config['department_id']


# 将查询数据格式化为字典
def format_info(data, keys):
    data_list = []
    for i in data:
        data_list.append(dict(zip(keys, i)))
    return data_list


# 将查询数据格式化为列表
def format_info2list(data):
    data_list = []
    for i in data:
        data_list.append(i[0])
    return data_list


# 鉴权语法糖，在只有管理员才能操作的路由前添加该语法糖
def authority(func):
    def wrapper(*args, **kwargs):
        if session.get('role') == 'admin':
            result = func(*args, **kwargs)
            role = '管理员'
        else:
            role = '普通用户'
            return jsonify({'code': 1, 'msg': '您当前没有权限操作此项目'})
        return result

    return wrapper





# 返回HOME页面html
@web_html.route('/home', methods=['GET'])
def home():
    if request.method == 'GET':
        tab_ch = request.args.get('to')

        if not session.get('login_state'):
            return redirect('/web/login')
        else:
            data = {
                'department': ["综合计划处", "党群人力处", "生产经营处", "技术处", "质量安全处", "物资供应处", "财务处", "审计处", "保障处", "修理项目部",
                               "制造分厂", "信息化中心",
                               "修理项目部", "海南分厂", "中转刻录", "集中文印室"],
                'state': ['开启', '关闭', '报警', '故障'],
                'position': [{'1-': '一楼'}, {'2-': '二楼'}, {'3-': '三楼'}, {'4-': '四楼'}, {'5-': '五楼'}, {'6-': '六楼'}],
                'self_ip': _config['self_ip'],
                'tab_ch': tab_ch
            }
            print(data)
            return render_template('home.html', **data)


@web_html.route('/index', methods=['GET'])
def index():
    if request.method == 'GET':
        if not session.get('login_state'):
            return redirect('/web/login')
        return render_template('./layout/index.html')

@web_html.route('/test', methods=['GET'])
def test():
    if request.method == 'GET':
        return render_template('./layout/index.html')

# 设备上报更新数据API
@web_html.route('/updateData', methods=['POST'])
def update_data():
    if request.method == 'POST':
        data = request.get_json()
        print(data)
        if not data:
            return jsonify({'success': False, 'msg': '无效的请求格式'}), 400
        # 增加字段存在性检查
        # try:
        ip = request.remote_addr
        # print(ip)
        # except:
        # ip = request.remote_addr
        sql_query = OpDevice()
        sql = OpRecord()
        info, keys = sql_query.get_device_info_ip(ip)

        data_info = format_info(info, keys)
        # print(data_info)
        if data_info:
            data_info = data_info[0]
            device_name = data_info['device_name']
            position = data_info['department'] + '----' + data_info['position']
            ip = data_info['ip']
            temperature = data['temp']
            humidity = data['humi']
            voltage = 220
            current = data['ec'] * 6.2
            # print(data)
            creat_time = data_info['creat_time']
            state = '关闭' if 0 <= int(current) <= 5 else '开启'
            # time_type = 'close_time' if state == '关闭' else 'open_time'

            ip_state = sql_query.get_device_state(ip)[0][0]
            # sql.updat_device_close_time(ip)
            time_type = 'update_time'
            if ip_state == '关闭' and state == '开启':
                time_type = 'open_time'
                time_temp_name = 'close_time'
                time_temp_value = ''
            elif ip_state == '开启' and state == '关闭':
                time_type = 'close_time'
                time_temp_name = 'open_time'
                time_temp_value = sql.get_device_open_time(ip)[0][0]
            elif ip_state == '开启' and state == '开启':
                time_temp_name = 'open_time'
                time_temp_value = sql.get_device_open_time(ip)[0][0]
            else:
                time_temp_name = 'close_time'
                time_temp_value = sql.get_device_close_time(ip)[0][0]

            sql_query.updat_device_state(state, ip)

            sql.add_device_data(device_name, position, ip, temperature, humidity, voltage, current, creat_time,
                                time_type, state, time_temp_name, time_temp_value)
            return jsonify({'success': True, 'msg': '提交成功'}), 200
        else:
            return jsonify({'success': False, 'msg': '未找到该设备信息'}), 200
    # if request.method == 'POST':
    #     data = request.get_json()
    #     if not data:
    #         return jsonify({'success': False, 'msg': '无效的请求格式'}), 400
    #     # 增加字段存在性检查
    #     # try:
    #     ip = data['ip']
    #     # except:
    #     # ip = request.remote_addr
    #     sql_query = OpDevice()
    #     sql = OpRecord()
    #     info, keys = sql_query.get_device_info_ip(ip)
    #
    #     data_info = format_info(info, keys)
    #     # print(data_info)
    #     if data_info:
    #         data_info = data_info[0]
    #         device_name = data_info['device_name']
    #         position = data_info['department'] + '----' + data_info['position']
    #         ip = data_info['ip']
    #         temperature = data['temperature']
    #         humidity = data['humidity']
    #         voltage = data['voltage']
    #         current = data['current']
    #         # print(data)
    #         creat_time = data_info['creat_time']
    #         state = '关闭' if 0 <= int(current) <= 5 else '开启'
    #         # time_type = 'close_time' if state == '关闭' else 'open_time'
    #
    #         ip_state = sql_query.get_device_state(ip)[0][0]
    #         # sql.updat_device_close_time(ip)
    #         time_type = 'update_time'
    #         if ip_state == '关闭' and state == '开启':
    #             time_type = 'open_time'
    #             time_temp_name = 'close_time'
    #             time_temp_value = ''
    #         elif ip_state == '开启' and state == '关闭':
    #             time_type = 'close_time'
    #             time_temp_name = 'open_time'
    #             time_temp_value = sql.get_device_open_time(ip)[0][0]
    #         elif ip_state == '开启' and state == '开启':
    #             time_temp_name = 'open_time'
    #             time_temp_value = sql.get_device_open_time(ip)[0][0]
    #         else:
    #             time_temp_name = 'close_time'
    #             time_temp_value = sql.get_device_close_time(ip)[0][0]
    #
    #         sql_query.updat_device_state(state, ip)
    #
    #         sql.add_device_data(device_name, position, ip, temperature, humidity, voltage, current, creat_time,
    #                             time_type, state, time_temp_name, time_temp_value)
    #         return jsonify({'success': True, 'msg': '提交成功'}), 200
    #     else:
    #         return jsonify({'success': False, 'msg': '未找到该设备信息'}), 200

# check_PC = Spider360()#
# check_PC.login('eppadmin', 'Humenjx@4801')#

# new_msg_queue = queue.Queue(maxsize=1)
# sock = MyWebSocket(new_msg_queue)

# 设备卡片数据获取API
@web_html.route('/getNewDeviceData', methods=['POST'])
def get_new_device_data():
    if request.method == 'POST':
        data = request.get_json()
        if data['page'] == '':
            page = 1
        else:
            page = int(data['page'])
        limit = int(data['limit'])
        offset = (int(page) - 1) * int(limit)

        op_device_sql = OpDevice()
        op_record_sql = OpRecord()
        ip_list = op_device_sql.get_all_ip()

        sql_str = 'select *, MAX(update_time) from deviceRecord where ip in '
        temp_str = '('
        for i in ip_list:
            temp_str += f"'{i[0]}', "

        sql_str += (temp_str + f') group by ip limit {limit} offset {offset};')

        data, keys = op_record_sql.exec_sql_str(sql_str.replace(', )', ')'))
        data = format_info(data, keys)
        # print(data)
        card_list = []

        # all_pc_data = check_PC.get_gorp()#
        # print('all_pc_data', all_pc_data)#

        # print(data)
        for i in data:
            department = i['position'].split("----")[0]
            # department_info = all_pc_data[department]#
            # print(department_info)  #
            # print(department, f'{department_info["online_num"]}/{department_info["total"]}')
            # all_pc_data[department][online_num]

            department_info = sock.get_online_info(_config['oa_department_data'][department]['id'])

            temperature_flag = 'normal' if 10 <= float(i['temperature']) <= 35 else 'warning'
            voltage_flag = 'normal' if 215 <= float(i['voltage']) <= 225 else 'danger'
            humidity_flag = 'normal' if 40 <= float(i['humidity']) <= 65 else 'warning'
            current_flag = 'normal' if 8 <= float(i['current']) <= 20 else 'danger'
            state = op_device_sql.get_device_state(i['ip'])[0][0]
            state = '开启' if 5 <= float(i['current']) else '关闭'
            card = f'''
                    <div class="layui-col-md3" style="">
                        <div class="compact-data-module {i['creat_time']}">
                            <div class="module-header">
                                <span>
                                    <i class="layui-icon layui-icon-location"></i>
                                    {i['position']}
                                </span>
                                
                                <span class="layui-badge {'layui-bg-green' if state == '开启' else 'layui-bg-gray'}">{state}</span>

                            </div>
                            <div class="card-ip" style="display:flex;justify-content:space-between">
                                <span >IP信息：{i['ip']}</span>
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-primary layui-border-green"
                                    id="department-online-num" value="{_config['oa_department_data'][department]['id']}">
                                    在线:{department_info["online_num"]}/{department_info["total"]}
                                </button>
                            </div>
                            <div class="module-body">
                                <div class="data-grid">
                                    <div class="data-item">
                                        <div class="data-lable">温度</div>
                                        <div class="data-value">
                                            <span class="status-indicator status-{temperature_flag}"></span>
                                            {i['temperature']}<span class="value-unit">℃</span>
                                        </div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-lable">电压</div>
                                        <div class="data-value">
                                            <span class="status-indicator status-{voltage_flag}"></span>
                                            {i['voltage']}<span class="value-unit">V</span>
                                        </div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-lable">湿度</div>
                                        <div class="data-value">
                                            <span class="status-indicator status-{humidity_flag}"></span>
                                            {i['humidity']}<span class="value-unit">%RH</span>
                                        </div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-lable">电流</div>
                                        <div class="data-value">
                                            <span class="status-indicator status-{current_flag}"></span>
                                            {i['current']}<span class="value-unit">A</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="updata-time">空调开启时间：{i['open_time']}</div>
                                <div class="updata-time">数据更新时间：{i['update_time']}</div>
                            </div>
                        </div>
                    </div>
                    '''
            card_list.append(card)
        temp_list = []

        for i in range(0, len(card_list), 4):
            child_row = card_list[i:i + 4]
            temp_list.append(child_row)
        html = ''
        for i in temp_list:
            html_str = '<div class="layui-row">'
            for j in i:
                html_str += j
            html_str += '</div>'
            html += html_str

        return_data = {
            'code': 0,
            'count': math.ceil(len(data) / 4),
            'html': html
        }
        return return_data


@web_html.route('/getCraneData', methods=['POST'])
def get_crane_data():
    if request.method == 'POST':
        data = request.get_json()



        # print(data)
        card_list = []

        # all_pc_data = check_PC.get_gorp()#
        # print('all_pc_data', all_pc_data)#

        # print(data)
        # for i in data:
        #     department = i['position'].split("----")[0]
        #     # department_info = all_pc_data[department]#
        #     # print(department_info)  #
        #     # print(department, f'{department_info["online_num"]}/{department_info["total"]}')
        #     # all_pc_data[department][online_num]
        #
        #     department_info = sock.get_online_info(_config['oa_department_data'][department]['id'])
        #
        #     temperature_flag = 'normal' if 10 <= float(i['temperature']) <= 35 else 'warning'
        #     voltage_flag = 'normal' if 215 <= float(i['voltage']) <= 225 else 'danger'
        #     humidity_flag = 'normal' if 40 <= float(i['humidity']) <= 65 else 'warning'
        #     current_flag = 'normal' if 8 <= float(i['current']) <= 20 else 'danger'

        card = f'''
                <div class="layui-col-md6" style="margin-bottom: 24px;">
                    <div class="modern-crane-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机
                            </div>
                            <div class="status-badge status-online">在线</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    25<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    26<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    19<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    6<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    15.5<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    8.2<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:30:25</div>
                        </div>
                    </div>
                </div>
                '''

        # 生成多个示例卡片以展示不同状态
        cards = []

        # 第一个卡片 - 正常状态
        card1 = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card">
                        <div class="connection-status"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机-01
                            </div>
                            <div class="status-badge status-online">在线</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item tooltip" data-tooltip="当前大车运行频率">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    45<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="当前小车运行频率">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    32<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="葫芦提升频率">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    28<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="当前起重载荷">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    8.5<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="大车当前位置">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    23.8<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="小车当前位置">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    12.4<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:32:15</div>
                        </div>
                    </div>
                </div>
                '''

        # 第二个卡片 - 警告状态
        card2 = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card">
                        <div class="connection-status warning"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机-02
                            </div>
                            <div class="status-badge status-warning">警告</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    58<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    41<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    22<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    15.2<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    45.6<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    8.9<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:31:58</div>
                        </div>
                    </div>
                </div>
                '''

        # 第三个卡片 - 正常状态
        card3 = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card">
                        <div class="connection-status"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机-03
                            </div>
                            <div class="status-badge status-online">在线</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item tooltip" data-tooltip="当前大车运行频率">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    38<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="当前小车运行频率">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    29<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="葫芦提升频率">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    31<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="当前起重载荷">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    4.2<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="大车当前位置">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    18.3<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="小车当前位置">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    6.7<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:33:02</div>
                        </div>
                    </div>
                </div>
                '''

        # 第四个卡片 - 离线状态
        card4 = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card">
                        <div class="connection-status offline"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机-04
                            </div>
                            <div class="status-badge status-offline">离线</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    0<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    0<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    0<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    0<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    0<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    0<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:25:12</div>
                        </div>
                    </div>
                </div>
                '''

        # 第五个卡片 - 正常状态
        card5 = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card">
                        <div class="connection-status"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机-05
                            </div>
                            <div class="status-badge status-online">在线</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    42<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    35<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    27<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    12.8<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    32.1<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    14.7<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:34:18</div>
                        </div>
                    </div>
                </div>
                '''

        # 第六个卡片 - 正常状态
        card6 = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card">
                        <div class="connection-status"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机-06
                            </div>
                            <div class="status-badge status-online">在线</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    39<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    48<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    33<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    7.3<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    28.9<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    11.2<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:33:45</div>
                        </div>
                    </div>
                </div>
                '''

        # 第七个卡片 - 警告状态
        card7 = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card">
                        <div class="connection-status warning"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-engine"></i>
                                LD分厂起重机-07
                            </div>
                            <div class="status-badge status-warning">警告</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <div class="data-label">大车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    52<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-danger"></span>
                                    55<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">葫芦频率</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    44<span class="value-unit">Hz</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">起重量</div>
                                <div class="data-value">
                                    <span class="status-indicator status-warning"></span>
                                    18.5<span class="value-unit">T</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">大车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    41.3<span class="value-unit">m</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">小车位置</div>
                                <div class="data-value">
                                    <span class="status-indicator status-normal"></span>
                                    16.8<span class="value-unit">m</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: *************</div>
                            <div class="update-time">更新时间: 2025-01-02 14:32:33</div>
                        </div>
                    </div>
                </div>
                '''

        cards = [card1, card2, card3, card4, card5, card6, card7]

        # 分页逻辑
        request_data = request.get_json() or {}
        page = int(request_data.get('page', 1))  # 当前页码，默认为1
        limit = 6  # 每页显示6个卡片
        total_count = len(cards)  # 总数据量

        # 计算分页
        start_index = (page - 1) * limit
        end_index = start_index + limit
        page_cards = cards[start_index:end_index]

        # 调试信息
        print(f"分页调试: page={page}, limit={limit}, total_count={total_count}")
        print(f"分页调试: start_index={start_index}, end_index={end_index}, page_cards_count={len(page_cards)}")

        # 生成HTML
        html_str = '<div class="layui-row">'
        for card in page_cards:
            html_str += card
        html_str += '</div>'

        return_data = {
            'code': 0,
            'count': total_count,
            'html': html_str
        }
        return return_data


@web_html.route('/UseIdGetPCInfo', methods=['POST', 'GET'])
def use_id_get_pc_info():
    if request.method == 'POST':
        json_data = request.get_json()
        # print(request.get_json())
        id_ = json_data['id']
        info = sock.get_online_info(id_)
        # print(info)
        return jsonify({"data": info})
    elif request.method == 'GET':

        data = sock.send_query('["623","157","367","98","425","95"]')
        # sock.close()
        return data


# 设备卡片过滤搜索API
@web_html.route('/searchCard', methods=['POST', 'GET'])
def search_card():
    if request.method == 'POST':
        data = request.get_json()
        page = int(data['page'])
        limit = int(data['limit'])
        offset = (int(page) - 1) * int(limit)
        op_device_sql = OpDevice()
        op_record_sql = OpRecord()

        sql_str = 'select *, MAX(update_time) from deviceRecord where '
        search_params = data['search_params']
        temp = 0

        # sql命令组合
        for key, value in search_params.items():
            if value != '' and (key == 'position' or key == 'department'):
                sql_str += f'and position like "%{value}%" ' if temp != 0 else f'position like "%{value}%" '
                temp += 1
            elif value != '' and key == 'AirConditioner_state':
                ip_list = op_device_sql.get_ip_from_devices_state(value)
                temp_sql_str = f'AirConditioner_state="{value}" and ip in {tuple(format_info2list(ip_list))} '
                sql_str += f'and {temp_sql_str}' if temp != 0 else temp_sql_str
                temp += 1
            elif value != '' and temp != 0:
                sql_str += f'and {key}="{value}" '
            elif value != '':
                sql_str += f' {key}="{value}" '
                temp += 1

        sql_str = (sql_str.replace(', )', ')') + f' group by ip limit {limit} offset {offset};')
        # print(sql_str)
        data, keys = op_record_sql.exec_sql_str(sql_str)
        data = format_info(data, keys)
        print(data, keys)
        card_list = []
        for i in data:
            temperature_flag = 'normal' if 10 <= float(i['temperature']) <= 35 else 'warning'
            voltage_flag = 'normal' if 215 <= float(i['voltage']) <= 225 else 'danger'
            humidity_flag = 'normal' if 40 <= float(i['humidity']) <= 65 else 'warning'
            current_flag = 'normal' if 8 <= float(i['current']) <= 20 else 'danger'
            data_list = op_device_sql.get_device_state(i['ip'])
            # print(i, data_list)
            if data_list:
                state = data_list[0][0]
            else:
                continue
            card = f'''
                <div class="layui-col-md3" style="">
                    <div class="compact-data-module {i['creat_time']}">
                        <div class="module-header">
                            <span>
                                <i class="layui-icon layui-icon-location"></i>
                                {i['position']}
                            </span>
                            <span class="layui-badge {'layui-bg-green' if state == '开启' else 'layui-bg-gray'}">{state}</span>
                        </div>
                        <div class="card-ip">IP信息：{i['ip']}</div>
                        <div class="module-body">
                            <div class="data-grid">
                                <div class="data-item">
                                    <div class="data-lable">温度</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{temperature_flag}"></span>
                                        {i['temperature']}<span class="value-unit">℃</span>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <div class="data-lable">电压</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{voltage_flag}"></span>
                                        {i['voltage']}<span class="value-unit">V</span>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <div class="data-lable">湿度</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{humidity_flag}"></span>
                                        {i['humidity']}<span class="value-unit">%RH</span>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <div class="data-lable">电流</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{current_flag}"></span>
                                        {i['current']}<span class="value-unit">A</span>
                                    </div>
                                </div>
                            </div>
                            <div class="updata-time">空调开启时间：{i['open_time']}</div>
                            <div class="updata-time">数据更新时间：{i['update_time']}</div>
                        </div>
                    </div>
                </div>
                '''
            card_list.append(card)

        temp_list = []

        for i in range(0, len(card_list), 4):
            child_row = card_list[i:i + 4]
            temp_list.append(child_row)
        # print(len(temp_list))
        html = ''
        for i in temp_list:
            html_str = '<div class="layui-row">'
            for j in i:
                html_str += j
            html_str += '</div>'
            html += html_str

        # print(len(data))
        return_data = {
            'code': 0,
            'count': math.ceil(len(data) / 4),
            'html': html
        }
        return return_data


# 登录API
@web_html.route('/login', methods=['POST', 'GET'])
def login_api():
    if request.method == 'POST':
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求格式'}), 400
        # 增加字段存在性检查
        username = data.get('username')
        password = data.get('password')
        if username == admin and password == admin_pwd:
            session['role'] = 'admin'
            session['login_state'] = 1
            return jsonify({'success': True, 'redirect': '/web/index'}), 200
        else:
            return jsonify({'success': False, 'error': '账号或密码错误'}), 200
    elif request.method == 'GET':
        if session.get('login_state'):
            return redirect('/web/index')
        else:
            return render_template('login.html')




