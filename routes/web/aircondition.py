# -*- coding:utf-8 -*-
import time
import random
from datetime import datetime
from flask import Blueprint, render_template, jsonify, request
import math

web_aircondition = Blueprint('aircondition', __name__)

# 模拟空调数据
def generate_aircondition_data():
    """生成模拟空调数据"""
    airconditions = []
    for i in range(1, 13):  # 生成12个空调设备
        # 随机生成状态
        status_options = ['在线', '离线', '警告', '故障']
        status = random.choice(status_options)
        
        # 根据状态生成不同的数据
        if status == '在线':
            temperature = round(random.uniform(20.0, 26.0), 1)
            humidity = round(random.uniform(40.0, 60.0), 1)
            voltage = round(random.uniform(220.0, 240.0), 1)
            current = round(random.uniform(2.0, 8.0), 1)
        elif status == '警告':
            temperature = round(random.uniform(18.0, 30.0), 1)
            humidity = round(random.uniform(30.0, 70.0), 1)
            voltage = round(random.uniform(200.0, 250.0), 1)
            current = round(random.uniform(1.0, 10.0), 1)
        elif status == '故障':
            temperature = round(random.uniform(15.0, 35.0), 1)
            humidity = round(random.uniform(20.0, 80.0), 1)
            voltage = round(random.uniform(180.0, 260.0), 1)
            current = round(random.uniform(0.5, 12.0), 1)
        else:  # 离线
            temperature = 0.0
            humidity = 0.0
            voltage = 0.0
            current = 0.0
        
        aircondition = {
            'id': i,
            'name': f'空调设备-{i:02d}',
            'ip': f'192.168.1.{100 + i}',
            'status': status,
            'temperature': temperature,
            'humidity': humidity,
            'voltage': voltage,
            'current': current,
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'position': f'{(i-1)//3 + 1}楼-{(i-1)%3 + 1}区域'
        }
        airconditions.append(aircondition)
    
    return airconditions

# 空调卡片数据获取API
@web_aircondition.route('/getAirconditionData', methods=['POST'])
def get_aircondition_data():
    if request.method == 'POST':
        data = request.get_json()
        
        # 生成空调数据
        airconditions = generate_aircondition_data()
        
        # 生成卡片HTML
        cards = []
        for ac in airconditions:
            # 根据状态设置样式类
            if ac['status'] == '在线':
                status_class = 'status-online'
                connection_class = ''
                indicator_class = 'status-normal'
            elif ac['status'] == '警告':
                status_class = 'status-warning'
                connection_class = 'warning'
                indicator_class = 'status-warning'
            elif ac['status'] == '故障':
                status_class = 'status-danger'
                connection_class = 'danger'
                indicator_class = 'status-danger'
            else:  # 离线
                status_class = 'status-offline'
                connection_class = 'offline'
                indicator_class = 'status-offline'
            
            card = f'''
                <div class="layui-col-md4" style="margin-bottom: 30px;">
                    <div class="modern-crane-card aircondition-card" data-id="{ac['id']}">
                        <div class="connection-status {connection_class}"></div>
                        <div class="card-header">
                            <div class="card-title">
                                <i class="layui-icon layui-icon-snowflake"></i>
                                {ac['name']}
                            </div>
                            <div class="status-badge {status_class}">{ac['status']}</div>
                        </div>
                        <div class="card-content">
                            <div class="data-item tooltip" data-tooltip="当前环境温度">
                                <div class="data-label">温度</div>
                                <div class="data-value">
                                    <span class="status-indicator {indicator_class}"></span>
                                    {ac['temperature']}<span class="value-unit">°C</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="当前环境湿度">
                                <div class="data-label">湿度</div>
                                <div class="data-value">
                                    <span class="status-indicator {indicator_class}"></span>
                                    {ac['humidity']}<span class="value-unit">%</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="当前工作电压">
                                <div class="data-label">电压</div>
                                <div class="data-value">
                                    <span class="status-indicator {indicator_class}"></span>
                                    {ac['voltage']}<span class="value-unit">V</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="当前工作电流">
                                <div class="data-label">电流</div>
                                <div class="data-value">
                                    <span class="status-indicator {indicator_class}"></span>
                                    {ac['current']}<span class="value-unit">A</span>
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="设备安装位置">
                                <div class="data-label">位置</div>
                                <div class="data-value">
                                    <span class="status-indicator {indicator_class}"></span>
                                    {ac['position']}
                                </div>
                            </div>
                            <div class="data-item tooltip" data-tooltip="设备IP地址">
                                <div class="data-label">IP地址</div>
                                <div class="data-value">
                                    <span class="status-indicator {indicator_class}"></span>
                                    {ac['ip']}
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-ip">IP: {ac['ip']}</div>
                            <div class="update-time">更新时间: {ac['update_time']}</div>
                        </div>
                        <div class="card-controls">
                            <div class="control-buttons">
                                <button class="control-btn power-btn {'active' if ac['status'] == '在线' else ''}" 
                                        data-action="power" data-id="{ac['id']}" 
                                        title="{'关闭' if ac['status'] == '在线' else '开启'}空调">
                                    <i class="layui-icon layui-icon-{'pause' if ac['status'] == '在线' else 'play'}"></i>
                                    {'关闭' if ac['status'] == '在线' else '开启'}
                                </button>
                                <button class="control-btn temp-down-btn" 
                                        data-action="temp-down" data-id="{ac['id']}" 
                                        title="降低温度">
                                    <i class="layui-icon layui-icon-subtraction"></i>
                                    温度-
                                </button>
                                <button class="control-btn temp-up-btn" 
                                        data-action="temp-up" data-id="{ac['id']}" 
                                        title="提高温度">
                                    <i class="layui-icon layui-icon-addition"></i>
                                    温度+
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            '''
            cards.append(card)
        
        # 分页逻辑
        request_data = request.get_json() or {}
        page = int(request_data.get('page', 1))  # 当前页码，默认为1
        limit = 6  # 每页显示6个卡片
        total_count = len(cards)  # 总数据量
        
        # 计算分页
        start_index = (page - 1) * limit
        end_index = start_index + limit
        page_cards = cards[start_index:end_index]
        
        # 生成HTML
        html_str = '<div class="layui-row">'
        for card in page_cards:
            html_str += card
        html_str += '</div>'
        
        return_data = {
            'code': 0,
            'count': total_count,
            'html': html_str
        }
        return return_data

# 空调控制API
@web_aircondition.route('/controlAircondition', methods=['POST'])
def control_aircondition():
    if request.method == 'POST':
        data = request.get_json()
        device_id = data.get('device_id')
        action = data.get('action')
        
        # 这里应该是实际的设备控制逻辑
        # 现在只是模拟返回成功
        
        if action == 'power':
            message = f'空调设备 {device_id} 电源状态已切换'
        elif action == 'temp-up':
            message = f'空调设备 {device_id} 温度已提高1度'
        elif action == 'temp-down':
            message = f'空调设备 {device_id} 温度已降低1度'
        else:
            message = '未知操作'
        
        return jsonify({
            'code': 0,
            'msg': message,
            'data': {
                'device_id': device_id,
                'action': action,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        })

# 空调页面路由
@web_aircondition.route('/aircondition', methods=['GET'])
def aircondition_page():
    if request.method == 'GET':
        data = {
            'department': ["综合计划处", "党群人力处", "生产经营处", "技术处", "质量安全处", "物资供应处", "财务处", "审计处", "保障处", "修理项目部",
                           "制造分厂", "信息化中心", "修理项目部", "海南分厂", "中转刻录", "集中文印室"],
            'state': ['在线', '离线', '警告', '故障'],
            'position': [{'1-': '一楼'}, {'2-': '二楼'}, {'3-': '三楼'}, {'4-': '四楼'}, {'5-': '五楼'}, {'6-': '六楼'}]
        }
        return render_template('aircondition_table.html', **data)

# 空调测试页面路由
@web_aircondition.route('/test', methods=['GET'])
def aircondition_test_page():
    if request.method == 'GET':
        data = {
            'department': ["综合计划处", "党群人力处", "生产经营处", "技术处", "质量安全处", "物资供应处", "财务处", "审计处", "保障处", "修理项目部",
                           "制造分厂", "信息化中心", "修理项目部", "海南分厂", "中转刻录", "集中文印室"],
            'state': ['在线', '离线', '警告', '故障'],
            'position': [{'1-': '一楼'}, {'2-': '二楼'}, {'3-': '三楼'}, {'4-': '四楼'}, {'5-': '五楼'}, {'6-': '六楼'}]
        }
        return render_template('aircondition_test.html', **data)
