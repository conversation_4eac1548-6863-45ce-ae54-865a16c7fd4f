<div class="layui-fluid">
    <div class="layui-row">
        {# 左侧导航栏 #}
        <div class="layui-col-md1">
            <ul class="layui-nav layui-nav-tree crane-left-nav" lay-filter="aircondition-left-nav"
                style="width: 100%; height: 100%">
                <li class="layui-nav-item crane-m-left-nav layui-this" id="aircondition-card">
                    <a><i class="layui-icon layui-icon-snowflake"></i>设备实况</a>
                </li>
                <li class="layui-nav-item crane-m-left-nav" id="aircondition-table">
                    <a><i class="layui-icon layui-icon-table"></i>空调列表</a>
                </li>
                <li class="layui-nav-item crane-m-left-nav" id="aircondition-add-pos">
                    <a><i class="layui-icon layui-icon-add-circle"></i>新增空调</a>
                </li>
                <li class="layui-nav-item crane-m-left-nav" id="aircondition-his-data">
                    <a><i class="layui-icon layui-icon-chart-screen"></i>历史数据</a>
                </li>
                <li class="layui-nav-item crane-m-left-nav" id="aircondition-bas-set">
                    <a><i class="layui-icon layui-icon-set"></i>基本设置</a>
                </li>
            </ul>
        </div>
        {# 右侧数据栏目 #}
        <div class="layui-col-md11">
            {# 新的Tab页面 #}
            <div class="layui-tab scrollable-tab" lay-filter="aircondition-l-tab" lay-allowClose="true">
                <ul class="layui-tab-title crane-tab-title-li">
                    <li class="layui-this " id="aircondition-card" lay-allowClose="false">设备实况</li>
                </ul>
                <div class="layui-tab-content crane-data-tab" id="aircondition-l-tab-page" style="">

                    <div class="layui-tab-item layui-show" style="" id="aircondition-card-li-content">
                        <div class="layui-fluid tab-child">
                            <div class="modern-search-container">
                                <div class="search-row">
                                    <div class="search-fields-group">
                                        <div class="search-field">
                                            <label>设备名称</label>
                                            <input type="text" name="TS-search-name" id="aircondition-TS-search-name"
                                                   placeholder="请输入设备名称">
                                        </div>
                                        <div class="search-field">
                                            <label>设备点位</label>
                                            <select name="aircondition-TS-search-position" id="aircondition-TS-search-position" class="modern-select">
                                                <option value="">请选择点位</option>
                                                {% for i in position %}
                                                    {% for key, value in i.items() %}
                                                        <option value="{{ key }}">{{ value }}</option>
                                                    {% endfor %}
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="search-field">
                                            <label>运行状态</label>
                                            <select name="aircondition-TS-search-state" id="aircondition-TS-search-state" class="modern-select">
                                                <option value="">请选择状态</option>
                                                {% for i in state %}
                                                    <option value="{{ i }}">{{ i }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="search-buttons">
                                        <button type="button" class="modern-btn modern-btn-primary aircondition-TS-search-btn" id="aircondition-TS-search-btn">
                                            <i class="layui-icon layui-icon-search"></i>
                                            搜索
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-secondary aircondition-TS-refresh-btn" id="aircondition-TS-refresh-btn">
                                            <i class="layui-icon layui-icon-refresh"></i>
                                            刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="aircondition-card-page">
                            </div>
                            <div id="aircondition_card_page_index"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>


<div class="layui-tab-item " style="" id="aircondition-table-li-content">
    <div class="layui-fluid tab-child">
        <div class="layui-row" id="aircondition-table-content">
            <div class="layui-col-md2 tab-input" style="">
                <label class="layui-form-label input-label search-label">名称搜索:</label>
                <input type="text" name="TS-search-name" id="aircondition-TS-search-name"
                       class="layui-input search-input"
                       placeholder="名称搜索" style="">
            </div>
            <div class="layui-col-md2 tab-input" style="">
                <label class="layui-form-label input-label search-label">IP搜索:</label>
                <input type="text" name="aircondition-TS-search-ip" id="aircondition-TS-search-ip"
                       class="layui-input search-input"
                       placeholder="IP搜索" style="">
            </div>
            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">部门搜索:</label>
                        <div class="layui-input-inline">
                            <select name="aircondition-TS-search-department"
                                    id="aircondition-TS-search-department"
                                    class=""
                                    style="padding-right: 10px">
                                <option value=""></option>
                                {% for i in department %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">点位搜索:</label>
                        <div class="layui-input-inline">
                            <select name="aircondition-TS-search-position" id="aircondition-TS-search-position"
                                    class=""
                                    style="padding-right: 10px">
                                <option value=""></option>
                                {% for i in position %}
                                    {% for key, value in i.items() %}
                                        <option value="{{ key }}">{{ value }}</option>
                                    {% endfor %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">状态搜索:</label>
                        <div class="layui-input-inline">
                            <select name="aircondition-TS-search-state" id="aircondition-TS-search-state"
                                    class="">
                                <option value=""></option>
                                {% for i in state %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 search-tool-btn" style="">
                <button type="button" class="layui-btn aircondition-TS-search-btn"
                        id="aircondition-TS-search-btn">搜索
                </button>
                <button type="button" class="layui-btn aircondition-TS-refresh-btn"
                        id="aircondition-TS-refresh-btn">
                    刷新
                </button>
            </div>
        </div>
        <hr>
        <table id="aircondition" style="width: 100%;height: 100%"></table>
        <script>

        </script>
        <script id="op-btn" type="text/html">
            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>

    </div>
</div>
