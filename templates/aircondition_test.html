<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>空调数据监控测试页面</title>
    
    <!-- 引入样式文件 -->
    <link href="/static/css/layui.css" rel="stylesheet" media="all">
    <link href="/static/css/lay.css" rel="stylesheet" media="all">
    <link href="/static/css/crane-modern.css" rel="stylesheet" media="all">
    <link href="/static/css/aircondition.css" rel="stylesheet" media="all">
    
    <!-- 引入脚本文件 -->
    <script src="/static/js/layui.js"></script>
    <script src="/static/js/jquery.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            font-size: 2.5rem;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-header p {
            font-size: 1.1rem;
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .test-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        
        .test-btn.secondary {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
        }
        
        .test-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6);
        }
        
        .status-info {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .status-info h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }
        
        .status-info p {
            margin: 5px 0;
            color: #6b7280;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .test-header h1 {
                font-size: 2rem;
            }
            
            .test-container {
                padding: 20px;
            }
            
            .test-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .test-btn {
                width: 200px;
            }
        }
    </style>
</head>

<body>
    <div class="test-header">
        <h1>🌡️ 空调数据监控测试页面</h1>
        <p>匹配起重机数据面板样式的空调设备监控界面</p>
    </div>
    
    <div class="test-container">
        <div class="status-info">
            <h3>📊 功能说明</h3>
            <p><strong>数据展示：</strong>IP地址、更新时间、温湿度、电流电压</p>
            <p><strong>控制功能：</strong>设备开关、温度调节（温度+、温度-）</p>
            <p><strong>界面特色：</strong>现代化卡片设计、响应式布局、实时数据更新</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="refreshData()">
                <i class="layui-icon layui-icon-refresh"></i>
                刷新数据
            </button>
            <button class="test-btn secondary" onclick="toggleAutoRefresh()">
                <i class="layui-icon layui-icon-play"></i>
                <span id="auto-refresh-text">开启自动刷新</span>
            </button>
            <button class="test-btn" onclick="showStats()">
                <i class="layui-icon layui-icon-chart"></i>
                统计信息
            </button>
        </div>
        
        <!-- 空调数据展示区域 -->
        {% include 'aircondition_table.html' %}
    </div>

    <!-- 引入空调页面脚本 -->
    <script src="/static/js/aircondition.js"></script>
    
    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;
        
        // 刷新数据
        function refreshData() {
            if (window.airconditionModule) {
                window.airconditionModule.refreshData();
                layer.msg('数据已刷新', {icon: 1});
            }
        }
        
        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = document.querySelector('.test-btn.secondary');
            const text = document.getElementById('auto-refresh-text');
            const icon = btn.querySelector('i');
            
            if (isAutoRefresh) {
                // 停止自动刷新
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                text.textContent = '开启自动刷新';
                icon.className = 'layui-icon layui-icon-play';
                btn.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
                layer.msg('自动刷新已停止', {icon: 0});
            } else {
                // 开启自动刷新
                autoRefreshInterval = setInterval(() => {
                    if (window.airconditionModule) {
                        window.airconditionModule.refreshData();
                    }
                }, 10000); // 每10秒刷新一次
                
                isAutoRefresh = true;
                text.textContent = '停止自动刷新';
                icon.className = 'layui-icon layui-icon-pause';
                btn.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
                layer.msg('自动刷新已开启（每10秒）', {icon: 1});
            }
        }
        
        // 显示统计信息
        function showStats() {
            const cards = document.querySelectorAll('.aircondition-card');
            let onlineCount = 0;
            let offlineCount = 0;
            let warningCount = 0;
            let errorCount = 0;
            
            cards.forEach(card => {
                const statusBadge = card.querySelector('.status-badge');
                if (statusBadge) {
                    const status = statusBadge.textContent.trim();
                    switch(status) {
                        case '在线':
                            onlineCount++;
                            break;
                        case '离线':
                            offlineCount++;
                            break;
                        case '警告':
                            warningCount++;
                            break;
                        case '故障':
                            errorCount++;
                            break;
                    }
                }
            });
            
            const total = cards.length;
            const onlineRate = total > 0 ? ((onlineCount / total) * 100).toFixed(1) : 0;
            
            layer.open({
                type: 1,
                title: '📈 设备统计信息',
                area: ['400px', '300px'],
                content: `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <strong>设备总数：</strong><span style="color: #667eea;">${total}</span>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>在线设备：</strong><span style="color: #22c55e;">${onlineCount}</span>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>离线设备：</strong><span style="color: #6b7280;">${offlineCount}</span>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>警告设备：</strong><span style="color: #f59e0b;">${warningCount}</span>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>故障设备：</strong><span style="color: #ef4444;">${errorCount}</span>
                        </div>
                        <div style="margin-bottom: 15px; padding-top: 10px; border-top: 1px solid #e5e7eb;">
                            <strong>在线率：</strong><span style="color: #667eea; font-size: 18px;">${onlineRate}%</span>
                        </div>
                    </div>
                `
            });
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示欢迎信息
            setTimeout(() => {
                layer.msg('空调监控系统已就绪', {icon: 1, time: 2000});
            }, 1000);
        });
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
