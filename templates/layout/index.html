<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="/static/layout/css/index.css">
    <title>数字化综合管理平台</title>
    <script src="/static/layout/js/jquery-2.2.1.min.js"></script>
    <script src="/static/layout/js/rem.js"></script>
    <script src="/static/layout/js/echarts.min.js"></script>
    <script src="/static/layout/js/guangxi.js"></script>
    <script src="/static/layout/js/index.js"></script>
    <link href="/static/css/layui.css" rel="stylesheet" media="all">
    <script src="/static/js/layui.js"></script>
    <script src="/static/js/panzoom.min.js"></script>
</head>
<body>
<div class="t_container">
    <header class="t_header">
        <span>数字化综合管理平台</span>
    </header>
    <main class="t_main">
        <div class="t_left_box">
            <img class="t_l_line" src="/static/layout/img/left_line.png" alt="">
            <div class="t_minbox">

                <span>仓库数据概览</span>

            </div>
            <div class="t_mbox t_rbox">
                <i></i>
                <span>库存总量</span>
                <h2>18000</h2>
            </div>
            <div class="t_mbox t_gbox">
                <i></i>
                <span>本月入库</span>
                <h2>1000</h2>
            </div>
            <div class="t_mbox t_ybox">
                <i></i>
                <span>本月出库</span>
                <h2>600</h2>
            </div>
            <img class="t_r_minline" src="/static/layout/img/right_line.png" alt="">
        </div>
        <div class="t_center_box">
            {#            <div class="t_top_box">#}
            {#                <img class="t_l_line" src="/static/layout/img/left_line.png" alt="">#}
            {#                <ul class="t_nav">#}
            {#                    <li>#}
            {#                        <span>空调设备数</span>#}
            {#                        <h1>100</h1>#}
            {#                        <i></i>#}
            {#                    </li>#}
            {#                    <li>#}
            {#                        <span>上月设备增加数</span>#}
            {#                        <h1>1</h1>#}
            {#                        <i></i>#}
            {#                    </li>#}
            {#                    <li>#}
            {#                        <span>运行中</span>#}
            {#                        <h1>31</h1>#}
            {#                    </li>#}
            {#                </ul>#}
            {#                <img class="t_r_line" src="/static/layout/img/right_line.png" alt="">#}
            {#            </div>#}
            <div class="t_bottom_box">
                <!--<img class="t_l_line" src="/static/layout/img/left_line.png" alt="">
                 <div id="chart_3" class="echart" style="width: 100%; height: 3.6rem;"></div> -->
                {#                    <img class="" src="/static/layout/img/layout.png" alt="" style="width: 682px; height: 317px">#}
                {#                <div class="container">#}
                {#                    <div class="background-img">#}
                {#                        <img class="background-img" src="/static/img/layout.png">#}
                {#                    </div>#}
                <div class="panzoom-container">
                    <img id="zoomable-img" src="/static/img/layout.png">
                    {#                        <div class="absolute-element" style="top: 50px; left:100px;">123456</div>#}


                    <div class="icon-container " id="icon-crane" style="top: 0; left: 0">
                        <i class="layui-icon layui-icon-engine" style="font-size: 20px; color: #00ffff"></i>
                        <div class="hover-box">
                            <h3>起重机</h3>
                            <p>IP：***********</p>
                        </div>
                    </div>

                    <div class="icon-container " id="icon-warehouse" style="top: 0; left: 0">
                        <i class="layui-icon layui-icon-template-1" style="font-size: 20px; color: #00ff00"></i>
                        <div class="hover-box">
                            <h3>仓库</h3>
                            <p>库存管理</p>
                        </div>
                    </div>

                    <div class="icon-container " id="icon-door" style="top: 0; left: 0">
                        <i class="layui-icon layui-icon-auz" style="font-size: 20px; color: #ff0000"></i>
                        <div class="hover-box">
                            <h3>保密部位</h3>
                            <p>需走流申请进入</p>
                        </div>
                    </div>

                    <div class="icon-container" id="icon-TH" style="top: 0; left: 0">
                        <i class="layui-icon layui-icon-water" style="font-size: 20px; color: #ff00ff"></i>

                        <div class="hover-box">
                            <h3>空调监控</h3>
                            <p>温湿度、空调状态、人员状态监控</p>
                        </div>
                    </div>

                    <div class="icon-container " id="icon-video" style="top: 50px; left: 50px">
                        <i class="layui-icon layui-icon-video" style="font-size: 20px; color: #ce8483"></i>
                        <div class="hover-box">
                            <h3>视频监控</h3>
                            {#                <p>123456789</p>#}
                        </div>
                    </div>

                </div>
                {#                </div>#}
            </div>
        </div>
        <div class="t_right_box">
            <img class="t_l_line" src="/static/layout/img/left_line.png" alt="">
            <div id="chart_4" class="echart" style="width: 100%; height: 4.6rem; position: absolute;"></div>
            {#            <header class="t_b_h">#}
            {#                <span>使用次数</span>#}
            {#                <img src="/static/layout/img/end.png"></img>#}
            {#                <h3>15<span>次</span></h3>#}
            {#            </header>#}
            {#            <main class="t_b_m">#}
            {#                <img src="/static/layout/img/map.png" alt="">#}
            {#                <div class="t_b_box">#}
            {#                    <span>溫度</span>#}
            {#                    <i></i>#}
            {#                    <h2>23℃</h2>#}
            {#                </div>#}
            {#                <div class="t_b_box1">#}
            {#                    <span>湿度</span>#}
            {#                    <i></i>#}
            {#                    <h2>56RH</h2>#}
            {#                </div>#}
            {#                <div class="t_b_box2">#}
            {#                    <span>信号</span>#}
            {#                    <i></i>#}
            {#                    <h2>-90dBm</h2>#}
            {#                </div>#}
            {#                <div class="t_b_box3">#}
            {#                    <span>光线</span>#}
            {#                    <i></i>#}
            {#                    <h2>250LX</h2>#}
            {#                </div>#}
            {#            </main>#}
            {#            <img class="t_r_line" src="/static/layout/img/right_line.png" alt="">#}
        </div>
        <div class="b_left_box">
            <img class="t_l_line" src="/static/layout/img/left_line.png" alt="">
            <div id="chart_2" class="echart" style="width: 100%; height: 3.6rem;"></div>
            <img class="t_r_line" src="/static/layout/img/right_line.png" alt="">
        </div>
        <div class="b_center_box">
            <img class="t_l_line" src="/static/layout/img/left_line.png" alt="">
            <div id="chart_1" class="echart" style="width: 100%; height: 3.6rem;"></div>
            <img class="t_r_line" src="/static/layout/img/right_line.png" alt="">
        </div>
        <div class="b_right_box">
            <img class="t_l_line" src="/static/layout/img/left_line.png" alt="">
            <h1 class="t_title">起重机设备维保数据查看</h1>
            <table class="t_table">
                <thead>
                <tr>
                    <th>维护时间</th>
                    <th>维保人</th>
                    <th>维保电话</th>
                    <th>维保部件</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>2025-02-06</td>
                    <td>张伟</td>
                    <td>13111345462</td>
                    <td>xxx</td>
                </tr>
                <tr>
                    <td>2025-02-06</td>
                    <td>张伟</td>
                    <td>13111345462</td>
                    <td>xxx</td>
                </tr>
                <tr>
                    <td>2025-02-06</td>
                    <td>张伟</td>
                    <td>13111345462</td>
                    <td>xxx</td>
                </tr>
                <tr>
                    <td>2018-02-06</td>
                    <td>张伟</td>
                    <td>13111345462</td>
                    <td>xxx</td>
                </tr>
                <tr>
                    <td>2025-02-06</td>
                    <td>张伟</td>
                    <td>13111345462</td>
                    <td>xxx</td>
                </tr>
                </tbody>
            </table>
            <img class="t_r_line" src="/static/layout/img/right_line.png" alt="">
        </div>
    </main>
</div>
<script>
    $(".icon-container").click(function () {
        console.log(this.id)
        if (this.id === 'icon-video') {
            layer.msg('当前模块还在开发中')
            return
        }

        window.location.href = '/web/home?to=' + this.id

    })

    document.addEventListener('DOMContentLoaded', function () {
        const elem = document.getElementById('zoomable-img')

        const panzoom_ = panzoom(elem, {
            maxScale: 5,
            minScale: 0.5,
            contain: "outside",
            startScale: 1,
            startX: 0,
            startY: 0
        })

        const container = document.getElementsByClassName('.panzoom-container')

        const icon_crane = document.querySelector('#icon-crane')
        const icon_warehouse = document.querySelector('#icon-warehouse')
        const icon_door = document.querySelector('#icon-door')
        const icon_TH = document.querySelector('#icon-TH')

        {#debugger#}
        {#elem.addEventListener('panzoomChange', (e) => {#}
        {#    debugger#}
        {#\})#}

        let imgNaturalWidth = 972.172;
        let imgNaturalHeight = 412.828;
        {##}

        icon_crane.dataset.originalY = '90';
        icon_crane.dataset.originalX = '430';

        icon_warehouse.dataset.originalY = '200';
        icon_warehouse.dataset.originalX = '600';

        icon_door.dataset.originalY = '251';
        icon_door.dataset.originalX = '356';

        icon_TH.dataset.originalY = '354';
        icon_TH.dataset.originalX = '516';
        {##}

        function updatePosition() {
            {#debugger#}
            const {x, y, scale} = panzoom_.getTransform();
            const displayWidth = elem.clientWidth;
            const displayHeight = elem.clientHeight;
            {#debugger#}
            {#console.log('getTransform', x, y, scale)#}
            const icon_craneXPercent = parseFloat(icon_crane.dataset.originalX) / imgNaturalWidth
            const icon_craneYPercent = parseFloat(icon_crane.dataset.originalY) / imgNaturalHeight
            const icon_craneX = x + (icon_craneXPercent * displayWidth * scale)
            const icon_craneY = y + (icon_craneYPercent * displayHeight * scale)
            icon_crane.style.transform = `matrix(${scale}, 0, 0, ${scale}, ${icon_craneX}, ${icon_craneY})`

            const icon_warehouseXPercent = parseFloat(icon_warehouse.dataset.originalX) / imgNaturalWidth
            const icon_warehouseYPercent = parseFloat(icon_warehouse.dataset.originalY) / imgNaturalHeight
            const icon_wareX = x + (icon_warehouseXPercent * displayWidth * scale)
            const icon_wareY = y + (icon_warehouseYPercent * displayHeight * scale)
            icon_warehouse.style.transform = `matrix(${scale}, 0, 0, ${scale}, ${icon_wareX}, ${icon_wareY})`


            const icon_doorXPercent = parseFloat(icon_door.dataset.originalX) / imgNaturalWidth
            const icon_doorYPercent = parseFloat(icon_door.dataset.originalY) / imgNaturalHeight
            const icon_doorX = x + (icon_doorXPercent * displayWidth * scale)
            const icon_doorY = y + (icon_doorYPercent * displayHeight * scale)
            icon_door.style.transform = `matrix(${scale}, 0, 0, ${scale}, ${icon_doorX}, ${icon_doorY})`

            const icon_THXPercent = parseFloat(icon_TH.dataset.originalX) / imgNaturalWidth
            const icon_THYPercent = parseFloat(icon_TH.dataset.originalY) / imgNaturalHeight
            const icon_THX = x + (icon_THXPercent * displayWidth * scale)
            const icon_THY = y + (icon_THYPercent * displayHeight * scale)
            icon_TH.style.transform = `matrix(${scale}, 0, 0, ${scale}, ${icon_THX}, ${icon_THY})`

            {#updatePosition()#}
        }

        panzoom_.on('transform', updatePosition);

        {#const iconx = imgNaturalWidth / 2#}
        {#const icony = imgNaturalHeight / 2#}

        {#const widthRatio = displayWidth / imgNaturalWidth;#}
        {#const heightRatio = displayHeight / imgNaturalHeight;#}


        {#const relativeX = (x / imgNaturalWidth) * displayWidth#}
        {#const relativeY = (y / imgNaturalHeight) * displayHeight#}




        {#console.log('relative', relativeX, relativeY)#}

        {#icon.style.transform = `translate(#}
        {#   calc(${x} + ${relativeX * scale}px,#}
        {#   calc(${y} + ${relativeY * scale}px#}
        {#   )`;#}
        {#icon.style.left = `${iconX + relativeX}`#}
        {#icon.style.top = `${iconY + relativeY}`#}

        {#icon_warehouse.style.transform = `matrix(${scale}, 0, 0, ${scale}, ${icon_wareX}, ${icon_wareY})`#}
        {#console.log(`matrix(${scale}, 0, 0, ${scale}, ${icon_wareX}, ${icon_wareY})`)#}
        {#icon.style.transform = `translate(${relativeX}px, ${relativeY}px) scale(${scale})`#}
        {#debugger#}
        {#\}#}

        {##}
        {#if (elem.complete && imgNaturalWidth) {#}
        {#    updatePosition()#}
        {#\} else {#}
        {#    elem.addEventListener('load', () => {#}
        {#        imgNaturalWidth = elem.naturalWidth;#}
        {#        imgNaturalHeight = elem.naturalHeight;#}
        {#        updatePosition()#}
        {#    })#}
        {#\}#}
        {##}

        {#const container = elem.parentElement#}
        {#container.on('wheel', panzoom_.zoomWithWheel);#}
        {#container.addEventListener('panzoomzoom', updatePosition);#}





        {#updatePosition()#}

        {#panzoom_.on('transform', (e) => {#}
        {#debugger#}
        {#    const transform = panzoom_.getTransform();#}
        {#    const {scale, x, y} = transform#}
        {#const {scale, x, y} = e.detail#}
        {#    icon.style.transform = `translate(${x}px, ${y}px) scale(${scale})`#}
        {#debugger#}
        {#$('.icon-container').each(function () {#}
        {#    const $el = $(this);#}
        {#    const originalLeft = parseFloat($el.data('original-left') || parseFloat($el.css('left')))#}
        {#    const originalTop = parseFloat($el.data('original-top') || parseFloat($el.css('top')))#}
        {##}
        {#    const newLeft = originalLeft * scale + x;#}
        {#    const newTop = originalTop * scale + y;#}
        {##}
        {#    $(this).css({#}
        {#        transform: `translate(${newLeft}px, ${newTop}px) scale(${scale})`,#}
        {#        transformOrigin: "0 0"#}
        {#    });#}
        {##}
        {#\});#}

        {#\});#}

        {#$('.icon-container').each(function () {#}
        {#    const $el = $(this)#}
        {#    $el.data('original-left') || parseFloat($el.css('left'))#}
        {#    $el.data('original-top') || parseFloat($el.css('top'))#}
        {#\});#}

        {#$(container).on('wheel', panzoom_.zoomWithWheel);#}
    })
</script>
</body>


</html>