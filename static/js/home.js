document.addEventListener("DOMContentLoaded", function () {
    var getNewDeviceData_url = '/web/getNewDeviceData'
    var getCraneCard_url = '/web/getCraneData'
    var deldevices_url = '/device/deldevices'
    var searchDevice_url = '/search/searchDevice'
    var searchCard_url = '/search/searchCard'
    var AirConditionData_url = '/device/AirConditionData'
    var editDevice_url = '/device/editDevice'
    var addDevice_url = '/device/addDevice'


    // debugger
    // 门禁表格初始化
    function init_door_table() {
        layui.use('table', function () {
            let AirCondition = layui.table;
            AirCondition.render({
                elem: '#door',
                id: 'door',
                // {#height: 100%,#}
                url: '/door/getInfo',
                limits: [10, 15, 20, 30, 50],
                page: true,
                cols: [[
                    {type: 'checkbox'},
                    {
                        field: 'id',
                        title: "ID",
                        width: 120,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'name',
                        title: "姓名",
                        width: 150,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'state',
                        title: "状态",
                        width: 120,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'start_time',
                        title: "开始时间",
                        width: 220,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'end_time',
                        title: "结束时间",
                        width: 220,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'img_url',
                        title: "头像链接",
                        width: 220,
                        align: 'center',
                        hide: true
                    },
                    {
                        field: 'operation',
                        title: "操作",
                        toolbar: '#op-btn',
                        width: 120,
                        sort: false, align: 'center'
                    },
                ]],
            })
        });
    }

    // 门禁左侧导航栏点击事件触发
    $('.door-m-left-nav').click(function () {

        // 删除多选删除按钮
        // $('#delete-btn').remove()
        // window.globalVal.deleteBtn = false

        var nav_id = $(this).attr('id')
        var click_id = nav_id + "-li"
        var text = $(this).text().trim().toString()
        var tab_title_list = $('.door-tab-title-li li').text().toString()
        if (tab_title_list.includes(text)) {
            layui.use(['element', 'form'], function () {
                var element = layui.element;
                var form = layui.form
                // debugger
                element.tabChange('door-l-tab', click_id);
                layui.element.render('tab')
                form.render()
            })
        } else {
            layui.use(['element', 'form'], function () {
                var element = layui.element;
                var form = layui.form
                element.tabAdd('door-l-tab', {
                    title: text,
                    content: $('#' + click_id + '-content').html(),
                    id: click_id
                });
                console.log('#' + click_id + '-content')
                element.tabChange('door-l-tab', click_id);
                console.log(click_id)
                // 刷新表单、tab
                layui.element.render('tab')
                form.render()
            })
        }
    })

    // 监听门禁检测tab切换事件
    layui.use(['element'], function () {
        var element = layui.element;
        element.on('tab(door-l-tab)', function (data) {
            console.log(data)

            // if (data.id === 'his-data-li') {
            //     show_history_date()
            // } else if (data.id === 'pos-list-li') {
            //     init_pos_list_table()
            // }
            try {
                var layid = data.id.slice(0, -3)
                // console.log(layid)
                // debugger
                $('#' + window.doorVal.lastChNav).removeClass('layui-this')
                window.doorVal.lastChNav = layid
                $('#' + layid).addClass('layui-this')
            } catch (e) {
                $('#' + window.doorVal.lastChNav).removeClass('layui-this')
            }
        })
    })

    // 门禁编辑和删除点击事件处理
    layui.use(['table', 'element', 'form'], function () {
        var table = layui.table
        var element = layui.element;
        var form = layui.form

        table.on('checkbox(AirConditioner)', function (obj) {
            var check_num = table.checkStatus('AirConditioner').data.length
            if (!window.doorVal.deleteBtn) {
                window.doorVal.deleteBtn = true
                $('.table-search-tool-btn').append('<button type="button" class="layui-btn layui-btn-danger" id="delete-btn">删除选中</button>')
            } else if (check_num === 0) {
                $('#delete-btn').remove()
                $('#delete-btn').remove()
                window.doorVal.deleteBtn = false
            }
            if (obj.type === 'all') {
                console.log('删除全部')
            } else {

            }
            // console.log(obj)
        })

        // 监听表格工具栏
        table.on('tool(door12)', function (obj) {
            var event = obj.event;
            console.log(event, obj.data)
            if (event === "edit") {
                var data = obj.data
                var department = data.department
                var device_name = data.device_name
                var ip = data.ip
                var port = data.port
                var position = data.position
                var creat_time = data.creat_time
                var state = data.state

                // $('#add_edit_name').val(device_name)
                // $('#add_edit_state').val(AirConditioner_state)
                // $('#add_edit_ip').val(ip)
                // $('#add_edit_port').val(port)
                // $('#add_edit_department').val(department)
                // $('#add_edit_position').val(position)
                // debugger
                element.tabDelete('door-l-tab', 'edit-pos')
                element.tabAdd('door-l-tab', {
                    title: '点位编辑',
                    content: $("#edit-pos-li-content").html(),
                    id: 'edit-pos'
                });
                $('#add_edit_name').val(device_name)
                $('#add_edit_state').val(state)
                $('#add_edit_ip').val(ip)
                $('#add_edit_port').val(port)
                $('#add_edit_department').val(department)
                $('#add_edit_position').val(position)
                $('#add-edit-create-time').val(creat_time)
                // debugger
                element.tabChange('door-l-tab', 'edit-pos');
                layui.element.render('tab')
                form.render()
            } else {
                layer.open({
                    title: false,
                    content: '确认删除该数据吗？',
                    btn: ['是', '否'],
                    yes: function (index, layero) {
                        layer.close(index)
                        $.ajax({
                            url: deldevices_url,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                creat_time: obj.data.creat_time
                            },
                            success: function (res) {
                                // console.log('响应结果', res)
                                table.reload('AirConditioner', {
                                    url: AirConditionData_url
                                })
                            }
                        })
                    },
                    btn2: function () {
                    }
                })
            }
        })
    })

    // 起重机表格初始化
    function init_crane_table() {
        layui.use('table', function () {
            let AirCondition = layui.table;
            AirCondition.render({
                elem: '#crane',
                id: 'crane',
                // {#height: 100%,#}
                url: '/device/AirConditionData',
                limits: [10, 15, 20, 30, 50],
                page: true,
                cols: [[
                    {type: 'checkbox'},
                    {
                        field: 'device_name',
                        title: "名称",
                        width: 120,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'department',
                        title: "起重机型号",
                        width: 120,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'position',
                        title: "起重机点位",
                        width: 120,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'ip',
                        title: "大车频率",
                        width: 150,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'port1',
                        title: "小车频率",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'port2',
                        title: "葫芦频率",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'state',
                        title: "起重量",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'update_time',
                        title: "大车位置",
                        width: 150,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'creat_time',
                        title: "小车位置",
                        width: 150,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'creat_role',
                        title: "创建用户",
                        width: 120,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'operation',
                        title: "操作",
                        toolbar: '#op-btn',
                        width: 120,
                        sort: false, align: 'center'
                    },
                ]],
            })
        });
    }

    // 起重机卡片初始化请求函数
    function requests_crane_card(page = 1) {

        console.log('加载起重机卡片')
        layui.use(['laypage'], function () {
            var laypage = layui.laypage;
            // debugger
            // if (request_card_flag) {
            console.log('初始加载: page =', page);
            $.ajax({
                url: getCraneCard_url,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    page: page,
                    limit: 6
                }),
                success: function (res) {
                    // request_card_flag = true

                    // if (load_flag_ === 1) {
                    //     layer.close(load_flag);
                    //     load_flag_ = 0;
                    // }

                    laypage.render({
                        elem: 'crane_card_page_index',
                        count: res.count,
                        limit: 6,
                        limits: [6, 12, 18, 24, 30],
                        curr: page,
                        jump: function (obj, first) {
                            // debugger
                            console.log(first)
                            var page = obj.curr
                            // debugger
                            if (page === "") {
                                page = 1
                            }
                            // console.log('!first && request_card_flag', !first && request_card_flag)
                            if (!first) {
                                console.log('分页跳转: page =', page);
                                $.ajax({
                                    url: getCraneCard_url,
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        page: page,
                                        limit: 6,
                                        // type: 'get'
                                    }),
                                    success: function (res) {

                                        console.log('success', request_card_flag)
                                        // if(res.html === ''){
                                        //     $('.card-page').html('<span>当前未查询到数据</span>')
                                        // }else {
                                        if (res.html === '') {
                                            $('.crane-card-page').html('<div class="none-span" ' +
                                                'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                                        } else {
                                            $('.crane-card-page').html(res.html)
                                            // 添加卡片动画效果
                                            animateCraneCards()
                                        }
                                    }
                                })
                                console.log('false', request_card_flag)
                                // request_card_flag = false
                            }
                        }
                    })
                    if (res.html === '') {
                        $('.crane-card-page').html('<div class="none-span" ' +
                            'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                    } else {
                        $('.crane-card-page').html(res.html)
                        // 添加卡片动画效果
                        animateCraneCards()
                    }
                }
            })
            // request_card_flag = false
            // }

        })
    }


    // 起重机左侧导航栏点击事件触发
    $('.crane-m-left-nav').click(function () {

        // 删除多选删除按钮
        // $('#delete-btn').remove()
        // window.globalVal.deleteBtn = false

        var nav_id = $(this).attr('id')
        var click_id = nav_id + "-li"
        var text = $(this).text().trim().toString()
        var tab_title_list = $('.door-tab-title-li li').text().toString()
        if (tab_title_list.includes(text)) {
            layui.use(['element', 'form'], function () {
                var element = layui.element;
                var form = layui.form
                // debugger
                element.tabChange('crane-l-tab', click_id);
                layui.element.render('tab')
                form.render()
            })
        } else {
            layui.use(['element', 'form'], function () {
                var element = layui.element;
                var form = layui.form
                element.tabAdd('crane-l-tab', {
                    title: text,
                    content: $('#' + click_id + '-content').html(),
                    id: click_id
                });
                console.log('#' + click_id + '-content')
                element.tabChange('crane-l-tab', click_id);
                console.log(click_id)
                // 刷新表单、tab
                layui.element.render('tab')
                form.render()
            })
        }
    })

    // 起重机检测tab切换事件监听
    layui.use(['element'], function () {
        var element = layui.element;
        element.on('tab(crane-l-tab)', function (data) {
            console.log(data)

            // if (data.id === 'his-data-li') {
            //     show_history_date()
            // } else if (data.id === 'pos-list-li') {
            //     init_pos_list_table()
            // }
            try {
                var layid = data.id.slice(0, -3)
                // console.log(layid)
                // debugger
                $('#' + window.craneVal.lastChNav).removeClass('layui-this')
                window.craneVal.lastChNav = layid
                $('#' + layid).addClass('layui-this')
            } catch (e) {
                $('#' + window.craneVal.lastChNav).removeClass('layui-this')
            }
        })
    })

    // // 起重机编辑和删除点击事件处理
    // layui.use(['table', 'element', 'form'], function () {
    //     var table = layui.table
    //     var element = layui.element;
    //     var form = layui.form
    //
    //     table.on('checkbox(AirConditioner)', function (obj) {
    //         var check_num = table.checkStatus('AirConditioner').data.length
    //         if (!window.doorVal.deleteBtn) {
    //             window.doorVal.deleteBtn = true
    //             $('.table-search-tool-btn').append('<button type="button" class="layui-btn layui-btn-danger" id="delete-btn">删除选中</button>')
    //         } else if (check_num === 0) {
    //             $('#delete-btn').remove()
    //             $('#delete-btn').remove()
    //             window.doorVal.deleteBtn = false
    //         }
    //         if (obj.type === 'all') {
    //             console.log('删除全部')
    //         } else {
    //
    //         }
    //         // console.log(obj)
    //     })
    //
    //     // 监听门禁表格工具栏
    //     table.on('tool(door)', function (obj) {
    //         var event = obj.event;
    //         console.log(event, obj.data)
    //         if (event === "edit") {
    //             var data = obj.data
    //             var department = data.department
    //             var device_name = data.device_name
    //             var ip = data.ip
    //             var port = data.port
    //             var position = data.position
    //             var creat_time = data.creat_time
    //             var state = data.state
    //
    //             // $('#add_edit_name').val(device_name)
    //             // $('#add_edit_state').val(AirConditioner_state)
    //             // $('#add_edit_ip').val(ip)
    //             // $('#add_edit_port').val(port)
    //             // $('#add_edit_department').val(department)
    //             // $('#add_edit_position').val(position)
    //             // debugger
    //             element.tabDelete('door-l-tab', 'edit-pos')
    //             element.tabAdd('door-l-tab', {
    //                 title: '点位编辑',
    //                 content: $("#edit-pos-li-content").html(),
    //                 id: 'edit-pos'
    //             });
    //             $('#add_edit_name').val(device_name)
    //             $('#add_edit_state').val(state)
    //             $('#add_edit_ip').val(ip)
    //             $('#add_edit_port').val(port)
    //             $('#add_edit_department').val(department)
    //             $('#add_edit_position').val(position)
    //             $('#add-edit-create-time').val(creat_time)
    //             // debugger
    //             element.tabChange('door-l-tab', 'edit-pos');
    //             layui.element.render('tab')
    //             form.render()
    //         } else {
    //             layer.open({
    //                 title: false,
    //                 content: '确认删除该数据吗？',
    //                 btn: ['是', '否'],
    //                 yes: function (index, layero) {
    //                     layer.close(index)
    //                     $.ajax({
    //                         url: deldevices_url,
    //                         type: 'POST',
    //                         dataType: 'json',
    //                         data: {
    //                             creat_time: obj.data.creat_time
    //                         },
    //                         success: function (res) {
    //                             // console.log('响应结果', res)
    //                             table.reload('AirConditioner', {
    //                                 url: AirConditionData_url
    //                             })
    //                         }
    //                     })
    //                 },
    //                 btn2: function () {
    //                 }
    //             })
    //         }
    //     })
    // })

    // 处室、车间弹窗在线数据显示
    $(document).on('click', '#department-online-num', function (data) {
        var content = `<div style="max-height: 400px; overflow-y: auto;">
            <table class="layui-table">
            <thead><tr><th>ID</th><th>使用人</th><th>状态</th></tr></thead>
            <tbody>`;
        // console.log('响应结果', data, this.value)
        $.ajax({
            url: '/web/UseIdGetPCInfo',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                id: this.value
            }),
            success: function (res) {
                var data_list = res.data.info
                console.log('响应结果', data_list)
                data_list.forEach(function (item) {

                    content += `<tr><td>${item.id}</td><td>${item.name}</td>
                    <td><span class="layui-badge ${item.statueColor}">${item.state}</span> </td></tr>`
                })
                content += '</tbody></table></div>'
                layer.open({
                    type: 1,
                    area: ['700px', '500px'],
                    title: '处室/车间 在线状态表',
                    closeBtn: 2,
                    // btn: ['关闭'],
                    // yes: function(index, layero){
                    //     layer.close(index)
                    // },
                    shadeClose: true,
                    content: content
                })
            }
        })
    })

    // 处室、车间弹窗在线数据显示  360版本
    // $(document).on('click', '#department-online-num', function (data) {
    //     var content = `<div style="max-height: 400px; overflow-y: auto;">
    //         <table class="layui-table">
    //         <thead><tr><th>IP</th><th>编号</th><th>责任人</th><th>状态</th></tr></thead>
    //         <tbody>`;
    //     // console.log('响应结果', data, this.value)
    //     $.ajax({
    //         url: '/web/UseIdGetPCInfo',
    //         type: 'POST',
    //         contentType: 'application/json',
    //         data: JSON.stringify({
    //             id: this.value
    //         }),
    //         success: function (res) {
    //             var data_list = res.data
    //             console.log('响应结果', data_list)
    //             data_list.forEach(function (item) {
    //                 var statueColor = item.state === "在线" ? "layui-bg-green" : "layui-bg-gray"
    //                 content += `<tr><td>${item.clientip}</td><td>${item.computername}</td><td>${item.username}</td>
    //                 <td><span class="layui-badge ${statueColor}">${item.state}</span> </td></tr>`
    //             })
    //             content += '</tbody></table></div>'
    //             layer.open({
    //                 type: 1,
    //                 area: ['700px', '500px'],
    //                 title: '处室/车间 在线状态表',
    //                 closeBtn: 2,
    //                 // btn: ['关闭'],
    //                 // yes: function(index, layero){
    //                 //     layer.close(index)
    //                 // },
    //                 shadeClose: true,
    //                 content: content
    //             })
    //         }
    //     })
    // })

    // 加载动画
    var load_flag = Object
    var load_flag_ = 1
    var time_out = 0

    var request_card_flag = true

    // 刷新设备点位列表
    function init_pos_list_table() {
        layui.use('table', function () {
            var AirCondition = layui.table;
            AirCondition.render({
                elem: '#AirConditioner',
                id: 'AirConditioner',
                height: 450,
                url: AirConditionData_url,
                page: true,
                cols: [[
                    {type: 'checkbox'},
                    {
                        field: 'device_name',
                        title: "名称",
                        width: 120,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'department',
                        title: "部门",
                        width: 120,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'position',
                        title: "点位",
                        width: 120,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'ip',
                        title: "IP",
                        width: 150,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'port',
                        title: "端口",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'state',
                        title: "状态",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'update_time',
                        title: "上次更新时间",
                        width: 150,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'creat_time',
                        title: "创建时间",
                        width: 150,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'creat_role',
                        title: "创建用户",
                        width: 120,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'operation',
                        title: "操作",
                        toolbar: '#op-btn',
                        width: 120,
                        sort: false, align: 'center'
                    },
                ]],
            })
        });

        // 重新渲染表格
        layui.use(['form'], function () {
            var form = layui.form;
            form.render('select')
        })
    }


    // 设备卡片初始化请求函数
    function requests_card(page = 1) {
        // debugger
        console.log('加载空调卡片')
        if(time_out === 1){
            stopTimer()
            return
        }
        layui.use(['laypage'], function () {
            var laypage = layui.laypage;
            // debugger
            if (request_card_flag) {
                $.ajax({
                    url: getNewDeviceData_url,
                    type: 'POST',
                    contentType: 'application/json',
                    timeout: 4000,
                    data: JSON.stringify({
                        page: page,
                        limit: 8
                    }),
                    success: function (res) {
                        request_card_flag = true
                        // debugger
                        // console.log(load_flag_)
                        if (load_flag_ === 1) {
                            layer.close(load_flag);
                            load_flag_ = 0;
                        }

                        laypage.render({
                            elem: 'card_page_index',
                            height: 300,
                            count: res.count,
                            limit: 1,
                            limits: [10, 15, 20, 30, 50],
                            curr: page,
                            jump: function (obj, first) {
                                // debugger
                                // console.log(first)
                                var page = obj.curr
                                // debugger
                                if (page === "") {
                                    page = 1
                                }
                                // console.log('!first && request_card_flag', !first && request_card_flag)
                                if (!first) {
                                    $.ajax({
                                        url: getNewDeviceData_url,
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({
                                            page: page,
                                            limit: 8,
                                            // type: 'get'
                                        }),
                                        success: function (res) {

                                            console.log('success', request_card_flag)
                                            // if(res.html === ''){
                                            //     $('.card-page').html('<span>当前未查询到数据</span>')
                                            // }else {
                                            if (res.html === '') {
                                                $('.card-page').html('<div class="none-span" ' +
                                                    'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                                            } else {
                                                $('.card-page').html(res.html)
                                            }
                                        }
                                    })
                                    console.log('false', request_card_flag)
                                    request_card_flag = false
                                }
                            }
                        })
                        if (res.html === '') {
                            $('.card-page').html('<div class="none-span" ' +
                                'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                        } else {
                            $('.card-page').html(res.html)
                        }
                    },
                    error: function (xhr, status, error) {
                        // debugger
                        time_out = 1
                        console.log(load_flag_, error)
                        if (status === "timeout") {
                            layer.close(load_flag);
                            load_flag_ = 0;
                            layer.msg("请检查OA是否重复登录")
                        } else {
                            layer.close(load_flag);
                            load_flag_ = 0;
                            layer.msg("请检查OA是否重复登录或重启服务")
                        }
                    }
                })
                request_card_flag = false

            }

        })
    }


    // 监听空调检测tab删除事件
    layui.use(['element'], function () {
        var element = layui.element;
        element.on('tabDelete(l-tab)', function (data) {
            $('#' + data.id.slice(0, -3)).removeClass('layui-this')
            console.log(data.id.slice(0, -3))

        })
    })

    // 监听空调检测tab切换事件
    layui.use(['element'], function () {
        var element = layui.element;
        element.on('tab(l-tab)', function (data) {
            console.log(data)
            if (data.index !== 0) {
                stopTimer()
            } else {
                // debugger
                startTimer()
            }
            if (data.id === 'his-data-li') {
                show_history_date()
            } else if (data.id === 'pos-list-li') {
                init_pos_list_table()
            }
            try {
                var layid = data.id.slice(0, -3)
                // console.log(layid)
                // debugger
                $('#' + window.globalVal.lastChNav).removeClass('layui-this')
                window.globalVal.lastChNav = layid
                $('#' + layid).addClass('layui-this')
            } catch (e) {
                $('#' + window.globalVal.lastChNav).removeClass('layui-this')
            }
        })
    })

    // 监听四小项tab切换事件
    layui.use(['element'], function () {
        var element = layui.element;
        element.on('tab(top-tab)', function (data) {

            // debugger
            if (data.index === 2) {
                $(".top-tab-btn").html('<button class="top-tab-xs-btn layui-btn layui-btn-xs layui-btn-radius layui-icon layui-icon-refresh">刷新</button>')
                $('.iframe_item').html('<iframe id="iframe" src="/proxy/Home/Index"\n' +
                    '                    height="800px" width="100%"\n' +
                    '                    sandbox="allow-same-origin allow-scripts allow-forms allow-top-navigation allow-downloads"\n' +
                    '                    allowfullscreen\n' +
                    '            ></iframe>')
            } else if (data.index === 3) {
                requests_crane_card()
                init_crane_table()
            } else if (data.index === 1) {
                init_door_table()
            } else {
                $(".top-tab-btn").text('库存管理')
            }
            if (data.index === 0) {
                time_out = 0
                load_flag = layer.load(0, {shade: [0.1, '#fff']});
                // debugger
                load_flag_ = 1
                requests_card()
                startTimer()
            } else {

                stopTimer()
            }


        })
    })

    // top—tab 刷新按钮监听
    $(document).on('click', '.top-tab-xs-btn', function () {
        const iframe = document.getElementById('iframe')
        // debugger
        iframe.contentWindow.location.reload();
    })

    window.globalVal = {
        deleteBtn: false,  // 多选删除框是否显示全局标志位
        lastChNav: '',  // tab和左侧导航栏互动全局标志位
        editTabShow: false  // 编辑tab是否已经打开
    }

    window.doorVal = {
        deleteBtn: false,  // 多选删除框是否显示全局标志位
        lastChNav: '',  // tab和左侧导航栏互动全局标志位
        editTabShow: false  // 编辑tab是否已经打开
    }
    window.craneVal = {
        deleteBtn: false,  // 多选删除框是否显示全局标志位
        lastChNav: '',  // tab和左侧导航栏互动全局标志位
        editTabShow: false  // 编辑tab是否已经打开
    }

    // 空调检测编辑和删除点击事件处理
    layui.use(['table', 'element', 'form'], function () {
        var table = layui.table
        var element = layui.element;
        var form = layui.form

        table.on('checkbox(AirConditioner)', function (obj) {
            var check_num = table.checkStatus('AirConditioner').data.length
            if (!window.globalVal.deleteBtn) {
                window.globalVal.deleteBtn = true
                $('.table-search-tool-btn').append('<button type="button" class="layui-btn layui-btn-danger" id="delete-btn">删除选中</button>')
            } else if (check_num === 0) {
                $('#delete-btn').remove()
                $('#delete-btn').remove()
                window.globalVal.deleteBtn = false
            }
            if (obj.type === 'all') {
                console.log('删除全部')
            } else {

            }
            // console.log(obj)
        })

        // 监听表格工具栏
        table.on('tool(AirConditioner)', function (obj) {
            var event = obj.event;
            console.log(event, obj.data)
            if (event === "edit") {
                var data = obj.data
                var department = data.department
                var device_name = data.device_name
                var ip = data.ip
                var port = data.port
                var position = data.position
                var creat_time = data.creat_time
                var state = data.state

                // $('#add_edit_name').val(device_name)
                // $('#add_edit_state').val(AirConditioner_state)
                // $('#add_edit_ip').val(ip)
                // $('#add_edit_port').val(port)
                // $('#add_edit_department').val(department)
                // $('#add_edit_position').val(position)
                // debugger
                element.tabDelete('l-tab', 'edit-pos')
                element.tabAdd('l-tab', {
                    title: '点位编辑',
                    content: $("#edit-pos-li-content").html(),
                    id: 'edit-pos'
                });
                $('#add_edit_name').val(device_name)
                $('#add_edit_state').val(state)
                $('#add_edit_ip').val(ip)
                $('#add_edit_port').val(port)
                $('#add_edit_department').val(department)
                $('#add_edit_position').val(position)
                $('#add-edit-create-time').val(creat_time)
                // debugger
                element.tabChange('l-tab', 'edit-pos');
                layui.element.render('tab')
                form.render()
            } else {
                layer.open({
                    title: false,
                    content: '确认删除该数据吗？',
                    btn: ['是', '否'],
                    yes: function (index, layero) {
                        layer.close(index)
                        $.ajax({
                            url: deldevices_url,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                creat_time: obj.data.creat_time
                            },
                            success: function (res) {
                                // console.log('响应结果', res)
                                table.reload('AirConditioner', {
                                    url: AirConditionData_url
                                })
                            }
                        })
                    },
                    btn2: function () {
                    }
                })
            }
        })
    })

    // 多选删除按钮点击事件监听
    $(document).on('click', '#delete-btn', function () {
        layui.use('table', function () {
            var table = layui.table
            var checkStatus = table.checkStatus('AirConditioner')
            // console.log(checkStatus.data)
            var creat_time_list_ = []
            for (let i of checkStatus.data) {
                creat_time_list_.push(i.creat_time)
            }
            // console.log(creat_time_list)
            layer.open({
                title: false,
                content: '确认删除该数据吗？',
                btn: ['是', '否'],
                yes: function (index, layero) {
                    layer.close(index)
                    $.ajax({
                        url: deldevices_url,
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            creat_time_list: creat_time_list_
                        }),
                        success: function (res) {
                            // 此处彻底删除按钮，否则页面切换会出现多按钮BUG
                            $('#delete-btn').remove()
                            $('#delete-btn').remove()
                            window.globalVal.deleteBtn = false
                            // console.log('响应结果', res)
                            table.reload('AirConditioner', {
                                url: AirConditionData_url
                            })
                        }
                    })
                },
                btn2: function () {
                }
            })
        })
    })

    // 事件委托，表单搜索数据过滤按钮监听
    $(document).on('click', '.search-btn', function () {

        var name = $('#search-name').val()
        var ip = $('#search-ip').val()
        var department = $('#search-department').val()
        var state = $('#search-state').val()

        if (name === '' && ip === '' && department === '' && state === '') {
            layer.msg('请选择一项搜索内容！')
            return;
        }
        layui.use('table', function () {
            var table = layui.table;
            table.reload('AirConditioner', {
                url: searchDevice_url,
                where: {
                    device_name: name,
                    ip: ip,
                    department: department,
                    state: state
                },
                page: {
                    curr: 1
                }
            })
        })
    })

    // 态势过滤搜索按钮监听
    $(document).on('click', '.TS-search-btn', function () {
        var name = $('#TS-search-name').val()
        var ip = $('#TS-search-ip').val()
        var department = $('#TS-search-department').val()
        var position = $('#TS-search-position').val()
        var state = $('#TS-search-state').val()

        if (name === '' && ip === '' && department === '' && state === '' && position === '') {
            layer.msg('请选择一项搜索内容！')
            // requests_card()
            return;
        }
        search_card_ajax()
    })

    function search_card_ajax() {
        var name = $('#TS-search-name').val()
        var ip = $('#TS-search-ip').val()
        var department = $('#TS-search-department').val()
        var position = $('#TS-search-position').val()
        var state = $('#TS-search-state').val()

        var page = $('#card_page_index .layui-laypage-curr').text()

        $.ajax({
            url: searchCard_url,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                page: page,
                limit: 8,
                search_params: {
                    department: department,
                    position: position,
                    device_name: name,
                    ip: ip,
                    AirConditioner_state: state
                }

            }),
            success: function (res) {
                // console.log('响应结果', res)
                layui.use(['laypage'], function () {
                    var laypage = layui.laypage;
                    laypage.render({
                        elem: 'card_page_index',
                        height: 300,
                        count: res.count,
                        limit: 1,
                        limits: [10, 15, 20, 30, 50],
                        jump: function (obj, first) {  // 分页数字点击回调
                            // console.log(obj, first)
                            if (!first) {
                                var page = obj.curr
                                $.ajax({
                                    url: searchCard_url,
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        page: page,
                                        limit: 8,
                                        search_params: {
                                            department: department,
                                            position: position,
                                            device_name: name,
                                            ip: ip,
                                            AirConditioner_state: state
                                        }
                                    }),
                                    success: function (res) {
                                        // debugger

                                        if (res.html === '') {
                                            $('.card-page').html('<div class="none-span" ' +
                                                'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                                        } else {
                                            $('.card-page').html(res.html)
                                        }
                                        // $('.card-page').html(res.html)
                                    }
                                })

                            }
                        }
                    })
                })
                if (res.html === '') {
                    $('.card-page').html('<div class="none-span" ' +
                        'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                } else {
                    $('.card-page').html(res.html)
                }
            }
        })

        // debugger

    }

    let timer = null

    // 开启实时请求定时器，用于空调数据实时轮询更新
    function startTimer() {
        if (timer) {
            console.log('计时器不可重复开启');
            return;
        }
        console.log('开启计时器')
        timer = setInterval(() => {

            var page = $('#card_page_index .layui-laypage-curr').text()
            var name = $('#TS-search-name').val()
            var ip = $('#TS-search-ip').val()
            var department = $('#TS-search-department').val()
            var position = $('#TS-search-position').val()
            var state = $('#TS-search-state').val()

            if (name === '' && ip === '' && department === '' && state === '' && position === '') {
                requests_card(page)
                return;
            }
            search_card_ajax()

            // console.log(new Data().toLocaleString())
            // requests_card()
        }, 5000)
    }

    // 关闭实时请求定时器
    function stopTimer() {
        if (timer) {
            console.log('关闭计时器')
            clearInterval(timer)
            timer = null
        }
    }

    // startTimer()


    // 态势刷新搜索按钮监听
    $(document).on('click', '.TS-refresh-btn', function () {
        $('#TS-search-name').val('')
        $('#TS-search-ip').val('')
        $('#TS-search-department').val('')
        $('#TS-search-position').val('')
        $('#TS-search-state').val('')
        layui.form.render('select')
        // console.log(2456678)
        requests_card()
    })

    // 数据表格刷新
    $(document).on('click', '.refresh-btn', function () {
        layui.use('table', function () {
            var table = layui.table;
            table.reload('AirConditioner', {
                url: AirConditionData_url,
                page: {
                    curr: 1
                }
            })
        })
        // console.log(123456)
        //     var name = $('#search-name').val()
        //     var ip = $('#search-ip').val()
        //     var department = $('#department').val()
        //     var state = $('#search-state').val()
        //     $.ajax({
        //         url: 'http://127.0.0.1:5000/web/searchDevice',
        //         type: 'POST',
        //         dataType: 'json',
        //         data: {
        //             name: name,
        //             ip: ip,
        //             department: department,
        //             state: state
        //         },
        //         success: function (res) {
        //             console.log(res)
        //         }
        //     })
    });

    // 编辑表单提交
    $(document).on('click', '.submit-edit-btn', function () {
        var device_name = $('#add_edit_name').val()
        var state = $('#add_edit_state').val()
        var ip = $('#add_edit_ip').val()
        var port = $('#add_edit_port').val()
        var department = $('#add_edit_department').val()
        var position = $('#add_edit_position').val()
        var creat_time = $('#add-edit-create-time').val()

        $.ajax({
            url: editDevice_url,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                device_name: device_name,
                department: department,
                position: position,
                state: state,
                ip: ip,
                port: port,
                creat_time: creat_time
            }),
            success: function (res) {
                if (res.code === 0) {
                    layer.msg(res.msg)
                    layui.use(['element'], function () {
                        var element = layui.element;
                        var table = layui.table;
                        element.tabDelete('l-tab', 'edit-pos')
                        table.reload('AirConditioner', {
                            url: AirConditionData_url,
                            page: {
                                curr: 1
                            }
                        })
                    })
                } else {
                    layer.msg(res.msg)
                }
            }
        })
    });

    // 添加设备表单提交
    $(document).on('click', '.add-device-btn', function () {
        var device_name = $('#add-device-name').val()
        var state = $('#add-device-state').val()
        var ip = $('#add-device-ip').val()
        var port = $('#add-device-port').val()
        var department = $('#add-device-department').val()
        var position = $('#add-device-position').val()


        $.ajax({
            url: addDevice_url,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                device_name: device_name,
                department: department,
                position: position,
                state: state,
                ip: ip,
                port: port,
            }),
            success: function (res) {
                if (res.code === 0) {
                    layer.msg(res.msg)
                    layui.use(['element'], function () {
                        var element = layui.element;
                        var table = layui.table;
                        element.tabDelete('l-tab', 'add-pos-li')
                        table.reload('AirConditioner', {
                            url: AirConditionData_url,
                            page: {
                                curr: 1
                            }
                        })
                    })
                } else {
                    layer.msg(res.msg)
                }
            }
        })
    });

    $(document).on('click', '.to-layout', function () {
        window.location.href = ''
    });

    // 空调左侧导航栏点击事件触发
    $('.m-left-nav').click(function () {

        // 删除多选删除按钮
        $('#delete-btn').remove()
        window.globalVal.deleteBtn = false

        var nav_id = $(this).attr('id')
        var click_id = nav_id + "-li"
        var text = $(this).text().trim().toString()
        var tab_title_list = $('.tab-title-li li').text().toString()
        if (tab_title_list.includes(text)) {
            layui.use(['element', 'form'], function () {
                var element = layui.element;
                var form = layui.form
                // debugger
                element.tabChange('l-tab', click_id);
                layui.element.render('tab')
                form.render()
            })
        } else {
            layui.use(['element', 'form'], function () {
                var element = layui.element;
                var form = layui.form
                element.tabAdd('l-tab', {
                    title: text,
                    content: $('#' + click_id + '-content').html(),
                    id: click_id
                });
                element.tabChange('l-tab', click_id);
                // 刷新表单、tab
                layui.element.render('tab')
                form.render()
            })
        }
    })

    $(document).on('click', '.compact-data-module', function (e) {
        var split_list = e.currentTarget.className.split(' ')
        var creat_time = split_list[1] + ' ' + split_list[2]
        console.log(creat_time)
        // debugger
    });


    // 历史数据统计
    function show_history_date() {
        var option_pie = {
            title: {
                text: '部门空调数量占比',
                subtext: '模拟数据',
                left: 'center'
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [{
                name: '部门占比',
                type: 'pie',
                radius: '50%',
                data: [
                    {value: 30, name: '技术处'},
                    {value: 100, name: '财务处'},
                    {value: 560, name: '综计处'},
                    {value: 300, name: '保障处'},
                    {value: 812, name: '人资处'},
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetx: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        }


        // 图标数据设置
        var option_column = {
            title: {
                text: '各部门用电量统计'
            },
            tooltip: {},
            legend: {
                data: ['用电量']
            },
            xAxis: {
                data: ['技术处', '财务处', '综计处', '保障处', '人资处']
            },
            yAxis: {},
            series: [{
                name: '用电量',
                type: 'bar',
                data: [5, 20, 36, 53, 24],
                showBackground: true,
                backgroundStyle: {
                    color: 'rgba(220, 220, 220, 0.8)'
                },
                itemStyle: {
                    barBorderRadius: 5,
                    borderWidth: 1,
                    borderType: 'solid',
                }
            }]
        }

        var option_line = {
            title: {
                text: '七日用电统计'
            },
            legend: {
                data: ['用电量']
            },
            xAxis: {
                data: ['xx月xx日', 'xx月xx日', 'xx月xx日', 'xx月xx日', 'xx月xx日', 'xx月xx日', 'xx月xx日']
            },
            yAxis: {},
            series: [{
                name: '用电量',
                type: 'line',
                data: [50, 20, 36, 53, 24, 88, 45],
                label: {
                    show: true,
                    position: 'bottom',
                    textStyle: {
                        fontSize: 12
                    }
                },
                // emphasis: {
                //     label: {
                //         show: true,
                //         position: 'top',
                //         textStyle: {
                //             fontSize: 12
                //         }
                //     }
                // },

            }]
        }

        var myChartPie = echarts.init(document.getElementById('echarts-pie'))
        var myChartColumn = echarts.init(document.getElementById('echarts-column'))
        var myChartLine = echarts.init(document.getElementById('echarts-line'))
        myChartPie.clear()
        myChartColumn.clear()
        myChartLine.clear()
        myChartPie.setOption(option_pie)
        myChartColumn.setOption(option_column)
        myChartLine.setOption(option_line)
    }

    console.log($(".tab_ch_flag")[0].id)
    layui.use(['element', 'form'], function () {
        var element = layui.element;
        var id = $(".tab_ch_flag")[0].id
        element.tabChange('top-tab', id + '-l');
        if (id === 'icon-TH' || id === 'None') {
            load_flag = layer.load(0, {shade: [0.1, '#fff']});
            requests_card()

        }
        // $('#' + id + '-l').click()

        // debugger
        // layui.element.render('tab')
    })

    // 起重机卡片动画效果函数
    function animateCraneCards() {
        const cards = document.querySelectorAll('.modern-crane-card');
        cards.forEach((card, index) => {
            // 初始状态
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            // 延迟动画，创建波浪效果
            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);
        });
    }

    // 搜索按钮点击效果
    $(document).on('click', '.modern-btn', function() {
        const btn = $(this);
        btn.addClass('clicked');
        setTimeout(() => {
            btn.removeClass('clicked');
        }, 200);
    });

    // 为现代化按钮添加点击动画样式
    const style = document.createElement('style');
    style.textContent = `
        .modern-btn.clicked {
            transform: scale(0.95) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
        }
    `;
    document.head.appendChild(style);

    // 模拟实时数据更新
    function simulateDataUpdate() {
        const dataValues = document.querySelectorAll('.modern-crane-card .data-value');
        dataValues.forEach(value => {
            // 随机选择一些数据项进行更新动画
            if (Math.random() < 0.3) {
                value.closest('.data-item').classList.add('data-updating');
                setTimeout(() => {
                    value.closest('.data-item').classList.remove('data-updating');
                }, 600);
            }
        });
    }

    // 每30秒模拟一次数据更新
    setInterval(simulateDataUpdate, 30000);

    // 工具提示功能增强
    $(document).on('mouseenter', '.tooltip', function() {
        const tooltip = $(this);
        const tooltipText = tooltip.attr('data-tooltip');
        if (tooltipText) {
            tooltip.addClass('tooltip-active');
        }
    });

    $(document).on('mouseleave', '.tooltip', function() {
        $(this).removeClass('tooltip-active');
    });

    // 连接状态监控模拟
    function updateConnectionStatus() {
        const statusIndicators = document.querySelectorAll('.connection-status');
        statusIndicators.forEach(indicator => {
            // 模拟连接状态变化
            const random = Math.random();
            if (random < 0.05) { // 5% 概率变为离线
                indicator.classList.add('offline');
                indicator.classList.remove('warning');
            } else if (random < 0.15) { // 10% 概率变为警告
                indicator.classList.add('warning');
                indicator.classList.remove('offline');
            } else { // 85% 概率保持正常
                indicator.classList.remove('offline', 'warning');
            }
        });
    }

    // 每10秒检查一次连接状态
    setInterval(updateConnectionStatus, 10000);
})