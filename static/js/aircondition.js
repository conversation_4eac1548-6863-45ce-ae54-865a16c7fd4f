document.addEventListener("DOMContentLoaded", function () {
    var getAirconditionData_url = '/aircondition/getAirconditionData'
    var controlAircondition_url = '/aircondition/controlAircondition'

    // 空调卡片初始化请求函数
    function requests_aircondition_card(page = 1) {
        console.log('加载空调卡片')
        layui.use(['laypage'], function () {
            var laypage = layui.laypage;
            console.log('初始加载: page =', page);
            $.ajax({
                url: getAirconditionData_url,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    page: page,
                    limit: 6
                }),
                success: function (res) {
                    laypage.render({
                        elem: 'aircondition_card_page_index',
                        count: res.count,
                        limit: 6,
                        limits: [6, 12, 18, 24, 30],
                        curr: page,
                        jump: function (obj, first) {
                            console.log(first)
                            var page = obj.curr
                            if (page === "") {
                                page = 1
                            }
                            if (!first) {
                                console.log('分页跳转: page =', page);
                                $.ajax({
                                    url: getAirconditionData_url,
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        page: page,
                                        limit: 6,
                                    }),
                                    success: function (res) {
                                        if (res.html === '') {
                                            $('.aircondition-card-page').html('<div class="none-span" ' +
                                                'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                                        } else {
                                            $('.aircondition-card-page').html(res.html)
                                            // 添加卡片动画效果
                                            animateAirconditionCards()
                                            // 重新绑定控制按钮事件
                                            bindControlEvents()
                                        }
                                    }
                                })
                            }
                        }
                    })
                    
                    if (res.html === '') {
                        $('.aircondition-card-page').html('<div class="none-span" ' +
                            'style="text-align: center; padding-top: 180px"><span>当前未查询到数据</span></div>')
                    } else {
                        $('.aircondition-card-page').html(res.html)
                        // 添加卡片动画效果
                        animateAirconditionCards()
                        // 绑定控制按钮事件
                        bindControlEvents()
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载空调数据失败:', error);
                    layer.msg('加载数据失败，请稍后重试', {icon: 2});
                }
            })
        });
    }

    // 空调卡片动画效果
    function animateAirconditionCards() {
        $('.aircondition-card').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(30px)'
            }).delay(index * 100).animate({
                'opacity': '1'
            }, 500).css('transform', 'translateY(0)');
        });
    }

    // 绑定控制按钮事件
    function bindControlEvents() {
        // 电源控制按钮
        $('.power-btn').off('click').on('click', function() {
            var deviceId = $(this).data('id');
            var action = $(this).data('action');
            controlDevice(deviceId, action, $(this));
        });

        // 温度减少按钮
        $('.temp-down-btn').off('click').on('click', function() {
            var deviceId = $(this).data('id');
            var action = $(this).data('action');
            controlDevice(deviceId, action, $(this));
        });

        // 温度增加按钮
        $('.temp-up-btn').off('click').on('click', function() {
            var deviceId = $(this).data('id');
            var action = $(this).data('action');
            controlDevice(deviceId, action, $(this));
        });
    }

    // 设备控制函数
    function controlDevice(deviceId, action, buttonElement) {
        // 显示加载状态
        var originalText = buttonElement.html();
        buttonElement.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>');
        buttonElement.prop('disabled', true);

        $.ajax({
            url: controlAircondition_url,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                device_id: deviceId,
                action: action
            }),
            success: function(res) {
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    
                    // 如果是电源控制，更新按钮状态
                    if (action === 'power') {
                        if (buttonElement.hasClass('active')) {
                            buttonElement.removeClass('active');
                            buttonElement.html('<i class="layui-icon layui-icon-play"></i>开启');
                            buttonElement.attr('title', '开启空调');
                        } else {
                            buttonElement.addClass('active');
                            buttonElement.html('<i class="layui-icon layui-icon-pause"></i>关闭');
                            buttonElement.attr('title', '关闭空调');
                        }
                    }
                    
                    // 延迟刷新数据以显示最新状态
                    setTimeout(function() {
                        requests_aircondition_card(1);
                    }, 1000);
                } else {
                    layer.msg(res.msg || '操作失败', {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.error('控制设备失败:', error);
                layer.msg('操作失败，请稍后重试', {icon: 2});
            },
            complete: function() {
                // 恢复按钮状态
                if (action !== 'power') {
                    buttonElement.html(originalText);
                }
                buttonElement.prop('disabled', false);
            }
        });
    }

    // 搜索功能
    function searchAircondition() {
        var searchName = $('#aircondition-TS-search-name').val();
        var searchPosition = $('#aircondition-TS-search-position').val();
        var searchState = $('#aircondition-TS-search-state').val();
        
        // 这里可以添加搜索逻辑
        console.log('搜索参数:', {
            name: searchName,
            position: searchPosition,
            state: searchState
        });
        
        // 重新加载第一页数据
        requests_aircondition_card(1);
    }

    // 刷新功能
    function refreshAircondition() {
        // 清空搜索条件
        $('#aircondition-TS-search-name').val('');
        $('#aircondition-TS-search-position').val('');
        $('#aircondition-TS-search-state').val('');
        
        // 重新加载数据
        requests_aircondition_card(1);
        layer.msg('数据已刷新', {icon: 1});
    }

    // 绑定搜索和刷新按钮事件
    $('#aircondition-TS-search-btn').on('click', searchAircondition);
    $('#aircondition-TS-refresh-btn').on('click', refreshAircondition);

    // 左侧导航栏点击事件
    layui.use('element', function(){
        var element = layui.element;
        
        // 监听导航点击
        element.on('nav(aircondition-left-nav)', function(elem){
            var navId = elem.attr('id');
            console.log('点击导航:', navId);
            
            switch(navId) {
                case 'aircondition-card':
                    // 显示设备实况
                    $('#aircondition-card-li-content').show();
                    $('#aircondition-table-li-content').hide();
                    requests_aircondition_card(1);
                    break;
                case 'aircondition-table':
                    // 显示空调列表
                    $('#aircondition-card-li-content').hide();
                    $('#aircondition-table-li-content').show();
                    // 这里可以初始化表格
                    break;
                case 'aircondition-add-pos':
                    layer.msg('新增空调功能开发中...', {icon: 0});
                    break;
                case 'aircondition-his-data':
                    layer.msg('历史数据功能开发中...', {icon: 0});
                    break;
                case 'aircondition-bas-set':
                    layer.msg('基本设置功能开发中...', {icon: 0});
                    break;
            }
        });
    });

    // 页面加载完成后初始化
    $(document).ready(function() {
        // 默认加载设备实况
        requests_aircondition_card(1);
        
        // 设置定时刷新（每30秒）
        setInterval(function() {
            if ($('#aircondition-card-li-content').is(':visible')) {
                requests_aircondition_card(1);
            }
        }, 30000);
    });

    // 导出函数供外部调用
    window.airconditionModule = {
        refreshData: function() {
            requests_aircondition_card(1);
        },
        searchData: searchAircondition,
        controlDevice: controlDevice
    };
});
