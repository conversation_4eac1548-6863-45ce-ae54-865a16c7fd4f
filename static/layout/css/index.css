.panzoom-container {
    position: relative;
    width: 99%;
    height: 99%;

    /*width: 970px;*/
    /*height: 403px;*/
    background-color: white;
    overflow: hidden;
    /*touch-action: none;*/
    border: 1px solid #2759a0;
}

#zoomable-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    /*display: block;*/
}



/*.container {*/
/*    position: relative;*/
/*    width: 879px;*/
/*    height: 418px;*/
/*    !*margin: 30px auto;*!*/
/*}*/

/*.background-img {*/
/*    width: 100%;*/
/*    height: 100%;*/
/*    !*background-image: url("/static/img/layout.png");*!*/
/*    background-size: cover;*/
/*    border-radius: 5px;*/
/*    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);*/
/*    margin-left: 50px;*/
/*    !*margin-left: 30px;*!*/
/*}*/

.icon-container {
    position: absolute;
    /*cursor: pointer;*/
    /*display: flex;*/
    /*transform-origin: 0 0;*/
    /*pointer-events: none;*/
}

.icon-container:hover {
    transform: scale(1.2);
}

.icon-container:hover .hover-box {
    display: block;
}

.hover-box {
    display: none;
    position: absolute;
    left: 50%;
    bottom: 100%;
    transform: translate(-50%);
    width: 70px;
    /*height: 70px;*/
    padding: 3px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    border-radius: 2px;
    margin-bottom: 5px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

}

.hover-box:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%);
    border-width: 8px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
}

.hover-box h3 {
    margin-bottom: 3px;
    font-size: 10px;
}

.hover-box p {
    font-size: 9px;
    line-height: 1.6;
    margin: 0;
}


html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

html {
    font-size: 100px;
}

ul, h1, h2, h3, h4, h5, h6, p {
    /*list-style: 0;*/
    padding: 0;
    margin: 0;
}

a {
    text-decoration: none;
}

/*正文*/
.t_container {
    width: 100%;
    height: 100vh;
    min-width: 1200px;
    background: url('../img/true.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.t_header {
    width: 100%;
    height: 80px;
    background: url('../img/linx.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    flex-shrink: 0;
}

.t_header span {
    color: #fff;
    font-size: 0.36rem;
    position: absolute;
    top: 50%;
    margin-top: -0.24rem;
    left: 9%;
}

.t_main {
    width: 100%;
    flex: 1;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 10px;
    padding: 10px;
    box-sizing: border-box;
    min-height: 0;
}

.t_left_box {
    position: relative;
    display: flex;
    flex-direction: column;
    text-align: center;
    grid-column: 1;
    grid-row: 1;
    min-width: 280px;
}

.t_l_line {
    position: absolute;
    top: 0;
    left: 0;
}

.t_r_line {
    position: absolute;
    bottom: 5px;
    right: 0;
}

.t_r_minline {
    position: absolute;
    bottom: -16px;
    right: 0;
}

.t_center_box {
    position: relative;
    display: flex;
    flex-direction: column;
    grid-column: 2;
    grid-row: 1;
    min-width: 400px;
}

.t_top_box {
    width: 100%;
    height: 1.13rem;
    overflow: hidden;
    position: relative;
    margin-bottom: 0.2rem;
}

.t_bottom_box {
    width: 100%;
    flex: 1;
    overflow: hidden;
    position: relative;
}

.t_right_box {
    position: relative;
    display: flex;
    flex-direction: column;
    grid-column: 3;
    grid-row: 1;
    min-width: 320px;
}

.b_left_box {
    position: relative;
    display: flex;
    flex-direction: column;
    grid-column: 1;
    grid-row: 2;
    min-width: 300px;
}

.b_center_box {
    position: relative;
    display: flex;
    flex-direction: column;
    grid-column: 2;
    grid-row: 2;
    min-width: 300px;
}

.b_right_box {
    position: relative;
    display: flex;
    flex-direction: column;
    grid-column: 3;
    grid-row: 2;
    min-width: 300px;
}

.t_mbox {
    width: 3rem;
    height: 1.28rem;
    position: relative;
    margin: 0 auto;
    margin-top: 0.18rem;
}

.t_minbox {
    width: 3rem;
    height: 0.1rem;
    position: relative;
    margin: 0 auto;
    margin-top: 0.1rem;

}

.t_rbox {
    background: #D9523F;
}

.t_gbox {
    background: #13D0B2;
}

.t_ybox {
    background: #F6A645;
}

.t_mbox i {
    display: inline-block;
    width: 0.46rem;
    height: 0.48rem;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20%;
    margin: auto;
}

.t_mbox h2 {
    font-size: 0.28rem;
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
}

.t_mbox span {
    font-size: 0.2rem;
    color: #fff;
    position: absolute;
    top: 24%;
    left: 48%;
}

.t_minbox i {
    display: inline-block;
    width: 0.46rem;
    height: 0.48rem;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20%;
    margin: auto;
}

.t_minbox h2 {
    font-size: 0.28rem;
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
}

.t_minbox span {
    font-size: 0.2rem;
    color: #fff;
    position: absolute;
    top: -38%;
    left: 33%;
}

.t_rbox i {
    background: url(../img/indent.png) no-repeat;
    background-size: 100% 100%;
}

.t_gbox i {
    background: url(../img/indent.png) no-repeat;
    /*background: url(../img/vip.png) no-repeat;*/
    background-size: 100% 100%;
}

.t_ybox i {
    background: url(../img/indent.png) no-repeat;
    /*background: url(../img/consumption.png) no-repeat;*/
    background-size: 100% 100%;
}

.t_nav {
    width: 100%;
    height: 100%;
}

.t_nav li {
    display: inline-block;
    width: 30%;
    height: 100%;
    text-align: center;
    position: relative;
}

.t_nav li span {
    font-size: 0.16rem;
    color: #1AA1FD;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    top: 25%;
}

.t_nav li h1 {
    font-size: 0.30rem;
    color: #fff;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    top: 50%;
}

.t_nav li i {
    width: 1px;
    height: 100%;
    position: absolute;
    right: -0.2rem;
    background: url('../img/sper.png') no-repeat;
    background-size: 100% 100%;
}

.t_table {
    font-size: 0.16rem;
    color: #fff;
    width: 94%;
    margin: 0 auto;
    border-spacing: 0;
    text-align: center;
    box-sizing: border-box;
    margin-top: 8%;
    max-height: 70%;
    overflow-y: auto;
}

.t_table tr {
    margin: 0;
    padding: 0;
    height: 0.42rem;
}

.t_table thead tr {
    background: #053A98;
}

.t_table tbody tr td:first-child {
    border-left: 1px solid #053A98;
}

.t_table td {
    border-bottom: 1px solid #053A98;
    border-right: 1px solid #053A98;
}

.t_title {
    position: absolute;
    font-size: 0.18rem;
    color: #fff;
    left: 5%;
    top: 10%;
}

.t_b_h, t_b_m {
    position: absolute;
    font-size: 0.16rem;
    left: 54%;
    width: 50%;
    height: 4.6rem;
}

.t_b_h span {
    position: absolute;
    color: #fff;
    top: 10%;
}

.t_b_h img {
    position: absolute;
    width: 0.53rem;
    height: 0.53rem;
    top: 6%;
    left: 24%
}

.t_b_h h3 {
    font-size: 0.36rem;
    color: #F0FF00;
    position: absolute;
    left: 55%;
    top: 8%;
    width: 1rem;
}

.t_b_h h3 span {
    font-size: 0.2rem;
    position: absolute;
    left: 50%;
    top: 28%;
    color: #0072FF;
}

.t_b_m img {
    position: absolute;
    left: 52%;
    top: 22%;
    border-top: 1px dotted #F0FF00;
    padding: 0 0.18rem;
    padding-top: 20px;
    width: 3.19rem;
    height: 1.67rem;
}

.t_b_box, .t_b_box1, .t_b_box2, .t_b_box3 {
    width: 1.3rem;
    height: 0.56rem;
    border: 1px dotted #F0FF00;
    border-radius: 5px;
    position: absolute;
}

.t_b_box {
    top: 68%;
    left: 56%;
}

.t_b_box span, .t_b_box1 span, .t_b_box2 span, .t_b_box3 span {
    font-size: 0.14rem;
    color: #fff;
    position: absolute;
    left: 10%;
}

.t_b_box i, .t_b_box1 i, .t_b_box2 i, .t_b_box3 i {
    width: 20px;
    height: 20px;
    position: absolute;
    top: 50%;
    left: 15%
}

.t_b_box i {
    background: url('../img/t.png') no-repeat;
    background-size: 100% 100%;
}

.t_b_box1 i {
    background: url('../img/s.png') no-repeat;
    background-size: 100% 100%;
}

.t_b_box2 i {
    background: url('../img/j.png') no-repeat;
    background-size: 100% 100%;
}

.t_b_box3 i {
    background: url('../img/g.png') no-repeat;
    background-size: 100% 100%;
}

.t_b_box h2, .t_b_box1 h2, .t_b_box2 h2, .t_b_box3 h2 {
    font-size: 0.18rem;
    color: #fff;
    position: absolute;
    top: 30%;
    left: 40%;
}

.t_b_box1 {
    top: 68%;
    left: 78%;
}

.t_b_box2 {
    top: 84%;
    left: 56%;
}

.t_b_box3 {
    top: 84%;
    left: 78%;
}


/* 移除原有的margin设置，因为现在使用grid gap */

/* 响应式设计 */
@media screen and (max-width: 1400px) {
    .t_main {
        grid-template-columns: 1fr 1.8fr 1fr;
    }

    .t_left_box, .t_right_box, .b_left_box, .b_right_box {
        min-width: 250px;
    }

    .t_center_box, .b_center_box {
        min-width: 350px;
    }
}

@media screen and (max-width: 1200px) {
    .t_container {
        min-width: 1000px;
    }

    .t_main {
        grid-template-columns: 1fr 1.5fr 1fr;
        gap: 8px;
        padding: 8px;
    }

    .t_left_box, .t_right_box, .b_left_box, .b_right_box {
        min-width: 220px;
    }

    .t_center_box, .b_center_box {
        min-width: 300px;
    }
}

@media screen and (max-width: 1000px) {
    .t_container {
        min-width: 900px;
    }

    .t_main {
        grid-template-columns: 1fr 1.2fr 1fr;
        gap: 6px;
        padding: 6px;
    }

    .t_left_box, .t_right_box, .b_left_box, .b_right_box {
        min-width: 200px;
    }

    .t_center_box, .b_center_box {
        min-width: 280px;
    }
}

/* 确保所有模块都能正确显示 */
.t_left_box, .t_center_box, .t_right_box,
.b_left_box, .b_center_box, .b_right_box {
    overflow: hidden;
    border: 1px solid transparent;
    box-sizing: border-box;
}

/* 图表容器自适应 */
.echart {
    width: 100% !important;
    height: 100% !important;
    min-height: 200px;
}

/* 地图容器优化 */
.panzoom-container {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: white;
    overflow: hidden;
    border: 1px solid #2759a0;
    min-height: 250px;
}

/* 确保表格在小屏幕下也能正常显示 */
@media screen and (max-width: 1000px) {
    .t_table {
        font-size: 0.14rem;
    }

    .t_title {
        font-size: 0.16rem;
    }

    .t_mbox h2 {
        font-size: 0.24rem;
    }

    .t_mbox span {
        font-size: 0.18rem;
    }
}

