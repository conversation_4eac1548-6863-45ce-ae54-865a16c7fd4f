/* 起重机模块现代化样式 */

/* 全局变量 */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 40px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.2);
    --border-radius: 16px;
    --border-radius-large: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 现代化卡片容器 */
.modern-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    padding: 20px 0;
}

/* 起重机卡片悬停效果增强 */
.modern-crane-card:hover .card-title i {
    transform: rotate(360deg);
    transition: transform 0.6s ease;
}

.modern-crane-card:hover .status-indicator {
    animation-duration: 1s;
}

/* 数据项悬停效果 */
.data-item:hover .data-value {
    color: #667eea;
    transform: scale(1.05);
}

.data-item:hover .status-indicator {
    transform: scale(1.3);
    box-shadow: 0 0 15px currentColor;
}

/* 状态徽章动画 */
.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.status-badge:hover::before {
    left: 100%;
}

/* 搜索容器增强效果 */
.modern-search-container:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* 输入框焦点效果增强 */
.search-field input:focus,
.search-field select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* 按钮悬停效果增强 */
.modern-btn {
    position: relative;
    overflow: hidden;
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:active {
    transform: translateY(-1px) scale(0.98);
}

/* 加载状态样式 */
.loading-card {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-large);
    height: 300px;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* 错误状态样式 */
.error-card {
    background: linear-gradient(145deg, #fef2f2 0%, #fee2e2 100%);
    border: 2px solid #fecaca;
    color: #dc2626;
}

.error-card .card-title i {
    color: #dc2626;
}

/* 离线状态样式 */
.offline-card {
    background: linear-gradient(145deg, #f9fafb 0%, #f3f4f6 100%);
    opacity: 0.7;
}

.offline-card .status-indicator {
    animation: none;
    background: #9ca3af;
}

/* 数据更新动画 */
@keyframes dataUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: #667eea; }
    100% { transform: scale(1); }
}

.data-updating .data-value {
    animation: dataUpdate 0.6s ease;
}

/* 连接状态指示器 */
.connection-status {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-gradient);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
    animation: pulse 2s infinite;
}

.connection-status.offline {
    background: var(--danger-gradient);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.connection-status.warning {
    background: var(--warning-gradient);
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 移动端优化 */
@media (max-width: 1024px) {
    .modern-card-container {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .modern-card-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .modern-crane-card {
        padding: 20px;
    }
    
    .card-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .data-item {
        padding: 12px;
    }
    
    .card-title {
        font-size: 16px;
    }
    
    .data-value {
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .card-content {
        grid-template-columns: 1fr;
    }
    
    .modern-search-container {
        padding: 16px;
    }
    
    .search-row {
        flex-direction: column;
        gap: 12px;
    }
    
    .modern-btn {
        width: 100%;
        justify-content: center;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(30, 30, 30, 0.95);
        --glass-border: rgba(255, 255, 255, 0.1);
    }
    
    .modern-crane-card {
        background: linear-gradient(145deg, #1f2937 0%, #111827 100%);
        color: #f9fafb;
    }
    
    .card-title {
        color: #f9fafb;
    }
    
    .data-label {
        color: #9ca3af;
    }
    
    .data-value {
        color: #f9fafb;
    }
    
    .modern-search-container {
        background: rgba(30, 30, 30, 0.95);
    }
    
    .search-field input,
    .search-field select {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .search-field label {
        color: #d1d5db;
    }
}
