/* 空调设备卡片特定样式 */

/* 空调卡片控制按钮区域 */
.card-controls {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.control-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.control-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px 12px;
    color: #475569;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 60px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 电源按钮样式 */
.power-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-color: #dc2626;
    color: white;
}

.power-btn.active {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-color: #16a34a;
}

.power-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-color: #b91c1c;
}

.power-btn.active:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    border-color: #15803d;
}

/* 温度控制按钮样式 */
.temp-down-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-color: #2563eb;
    color: white;
}

.temp-down-btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-color: #1d4ed8;
}

.temp-up-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-color: #d97706;
    color: white;
}

.temp-up-btn:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    border-color: #b45309;
}

/* 按钮禁用状态 */
.control-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.control-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 空调卡片特殊状态样式 */
.aircondition-card.status-cooling::before {
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #06b6d4 100%);
}

.aircondition-card.status-heating::before {
    background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #ef4444 100%);
}

.aircondition-card.status-offline {
    opacity: 0.6;
    background: linear-gradient(145deg, #f9fafb 0%, #f3f4f6 100%);
}

/* 温度显示特殊样式 */
.data-item .temperature-value {
    font-weight: 600;
    color: #1f2937;
}

.data-item .temperature-cold {
    color: #3b82f6;
}

.data-item .temperature-hot {
    color: #ef4444;
}

.data-item .temperature-normal {
    color: #22c55e;
}

/* 湿度显示特殊样式 */
.data-item .humidity-value {
    font-weight: 600;
}

.data-item .humidity-low {
    color: #f59e0b;
}

.data-item .humidity-high {
    color: #3b82f6;
}

.data-item .humidity-normal {
    color: #22c55e;
}

/* 电压电流显示样式 */
.data-item .voltage-value,
.data-item .current-value {
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.data-item .voltage-normal,
.data-item .current-normal {
    color: #22c55e;
}

.data-item .voltage-warning,
.data-item .current-warning {
    color: #f59e0b;
}

.data-item .voltage-danger,
.data-item .current-danger {
    color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .control-buttons {
        flex-direction: column;
        gap: 6px;
    }
    
    .control-btn {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .card-controls {
        margin-top: 12px;
        padding-top: 12px;
    }
    
    .control-btn {
        padding: 6px 10px;
        font-size: 11px;
    }
}

/* 加载动画 */
.control-btn .layui-anim-rotate {
    animation-duration: 1s;
}

/* 状态指示器特殊样式 */
.status-indicator.status-cooling {
    background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
    animation: pulse-blue 2s infinite;
}

.status-indicator.status-heating {
    background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
    animation: pulse-orange 2s infinite;
}

@keyframes pulse-blue {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
    }
}

@keyframes pulse-orange {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(245, 158, 11, 0);
    }
}

/* 卡片悬停效果增强 */
.aircondition-card:hover .control-btn {
    transform: translateY(-1px);
}

.aircondition-card:hover .card-controls {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin: 16px -8px -8px -8px;
    padding: 16px 8px 8px 8px;
}

/* 工具提示样式 */
.control-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 4px;
}

.control-btn[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}
