.tab-input {
    padding-right: 10px;
}

#left-nav {
    width: 100%;
    height: 100%
}

.input-label {
    padding-left: 1pt;
    padding-right: 0;
    text-align: center;
}

.tab-child {
    padding-right: 0;
    padding-left: 0;
}

.search-input {
    width: 60%;
}

.layui-select-title {
    /*padding-right: 10px;*/
    padding-right: 0;
}

.layui-input-inline {
    width: 60% !important;
}

.tab-layui-form-item {
    margin-bottom: 0;
}
.data-tab {
    padding: 0;
    padding-top: 10px
}

.search-label {
    width: 65px !important;
}

.info-list-btn{
    width: 100px;
    height: 100px;
    padding: 0;
}


/*数据面板*/
.compact-data-module{
    font-family: 'Helvetica Neue', Arial, sans-serif;
    /*max-width: 900px;*/
    margin-left: 20px;
    margin-top: 20px;
    width: 260px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    font-size: 13px;
}
.module-header{
    padding: 5px 15px;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.module-body{
    padding: 0 12px 12px 15px;
}
.data-grid{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}
.data-label{
    color: #666;
    font-size: 12px;
    margin-bottom: 2px;
}
.data-value{
    font-size: 13px;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
}
.value-unit{
    font-size: 11px;
    color: #999;
    margin-left: 2px;
    font-weight: normal;
}
.status-indicator{
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}
.status-normal{
    background: #52c41a;
}
.status-warning{
    background: #faad14;
}
.status-danger{
    background: #f5222d;
}
.location-info{
    grid-column: span 2;
    padding-bottom: 8px;
    border-bottom: 1px dashed #eee;
    margin-bottom: 8px;
}
.updata-time{
    font-size: 11px;
    color: #888;
    text-align: right;
    margin-top: 5px;
}
.card-ip{
    font-size: 8px;
    color: #888;
    text-align: left;
    margin-top: 3px;
    padding-left: 15px;
    padding-right: 15px;
}

/*右侧tab页面滚动*/
.scrollable-tab{
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin-top: 0;
}

.layui-tab-content {
    flex: 1;
    overflow: auto;
    padding: 16px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
    z-index: 1;
}

#card_page_index {
    text-align: center;
    margin-top: 20px;
}

.card-page {
    min-height: 500px;
    padding: 20px 0;
}

#crane_card_page_index{
    text-align: center;
    margin-top: 10px;
    margin-bottom: 20px;
}

.crane-card-page{
    min-height: auto;
    padding: 20px 0 10px 0;
}

/* 卡片容器间距优化 */
.crane-card-page .layui-row {
    margin-left: -20px;
    margin-right: -20px;
}

.crane-card-page .layui-col-md4 {
    padding-left: 20px;
    padding-right: 20px;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

/* 确保卡片在较小屏幕上的显示 */
@media (min-width: 992px) {
    .crane-card-page .layui-col-md4 {
        width: 33.333333%;
        float: left;
    }
}

/* 现代化搜索栏样式 */
.modern-search-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
}

.search-fields-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    flex: 1;
}

.search-field {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 180px;
    flex: 1;
}

.search-field label {
    font-size: 13px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
    min-width: 70px;
}

.search-field input,
.search-field select,
#crane-TS-search-position,
#crane-TS-search-state {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 13px;
    transition: all 0.3s ease;
    background: white;
    min-width: 100px;
}

.search-field input:focus,
.search-field select:focus,
#crane-TS-search-position:focus,
#crane-TS-search-state:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* 选择框下拉选项样式 - 现代化美化 */
.search-field select option {
    padding: 12px 16px;
    background: white;
    color: #374151;
    border: none;
    font-size: 13px;
    line-height: 1.5;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid #f1f5f9;
    position: relative;
}

.search-field select option:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    color: #9ca3af;
    font-style: italic;
}

.search-field select option:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    border-bottom: none;
}

.search-field select option:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #667eea;
    transform: translateX(4px);
    box-shadow: inset 4px 0 0 #667eea;
    font-weight: 500;
}

.search-field select option:checked,
.search-field select option:selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.2);
}

/* 自定义选择框样式 - 现代化美化 */
.search-field select,
#crane-TS-search-position,
#crane-TS-search-state {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 12px center !important;
    background-size: 18px !important;
    padding-right: 44px !important;
    cursor: pointer !important;
    position: relative !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.search-field select:hover,
#crane-TS-search-position:hover,
#crane-TS-search-state:hover {
    border-color: #667eea !important;
    background-color: #f8fafc !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
    transform: translateY(-1px) !important;
}

.search-field select:focus,
#crane-TS-search-position:focus,
#crane-TS-search-state:focus {
    border-color: #667eea !important;
    background-color: white !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15) !important;
    transform: translateY(-1px) !important;
}

/* 强制应用起重机模块下拉框样式 - 最高优先级 */
.modern-search-container select,
.modern-search-container #crane-TS-search-position,
.modern-search-container #crane-TS-search-state,
div.modern-search-container select[name="crane-TS-search-position"],
div.modern-search-container select[name="crane-TS-search-state"] {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background: white url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230270c1' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") no-repeat right 12px center !important;
    background-size: 18px !important;
    padding: 8px 44px 8px 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    font-size: 13px !important;
    cursor: pointer !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
}

.modern-search-container select:hover,
.modern-search-container #crane-TS-search-position:hover,
.modern-search-container #crane-TS-search-state:hover {
    border-color: #0270c1 !important;
    background-color: #e6f3ff !important;
    box-shadow: 0 4px 12px rgba(2, 112, 193, 0.15) !important;
    transform: translateY(-1px) !important;
}

.modern-search-container select:focus,
.modern-search-container #crane-TS-search-position:focus,
.modern-search-container #crane-TS-search-state:focus {
    outline: none !important;
    border-color: #0270c1 !important;
    background-color: white !important;
    box-shadow: 0 0 0 3px rgba(2, 112, 193, 0.1), 0 4px 12px rgba(2, 112, 193, 0.15) !important;
    transform: translateY(-1px) !important;
}

/* 下拉选项容器样式 */
.search-field select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 强制应用下拉选项样式 - 最高优先级 */
.modern-search-container select option,
.modern-search-container #crane-TS-search-position option,
.modern-search-container #crane-TS-search-state option,
select[name="crane-TS-search-position"] option,
select[name="crane-TS-search-state"] option {
    padding: 12px 16px !important;
    background: white !important;
    color: #374151 !important;
    border: none !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
    border-bottom: 1px solid #f1f5f9 !important;
}

.modern-search-container select option:hover,
.modern-search-container #crane-TS-search-position option:hover,
.modern-search-container #crane-TS-search-state option:hover,
select[name="crane-TS-search-position"] option:hover,
select[name="crane-TS-search-state"] option:hover {
    background: #e6f3ff !important;
    color: #0270c1 !important;
    font-weight: 500 !important;
}

.modern-search-container select option:checked,
.modern-search-container select option:selected,
select[name="crane-TS-search-position"] option:checked,
select[name="crane-TS-search-position"] option:selected,
select[name="crane-TS-search-state"] option:checked,
select[name="crane-TS-search-state"] option:selected {
    background: #0270c1 !important;
    color: white !important;
    font-weight: 600 !important;
}

/* 空选项特殊样式 */
.modern-search-container select option[value=""],
select[name="crane-TS-search-position"] option[value=""],
select[name="crane-TS-search-state"] option[value=""] {
    color: #9ca3af !important;
    font-style: italic !important;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
}

/* 终极CSS重置 - 覆盖所有可能的样式冲突 */
#crane-card-li-content select,
#crane-TS-search-position,
#crane-TS-search-state {
    /* 重置所有可能的样式 */
    all: unset !important;
    /* 重新应用我们的样式 */
    display: inline-block !important;
    width: 100% !important;
    padding: 8px 44px 8px 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    background: white url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") no-repeat right 12px center !important;
    background-size: 18px !important;
    font-size: 13px !important;
    font-family: inherit !important;
    color: #374151 !important;
    cursor: pointer !important;
    box-sizing: border-box !important;
    transition: all 0.3s ease !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
}

#crane-card-li-content select:hover,
#crane-TS-search-position:hover,
#crane-TS-search-state:hover {
    border-color: #667eea !important;
    background-color: #f8fafc !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

#crane-card-li-content select:focus,
#crane-TS-search-position:focus,
#crane-TS-search-state:focus {
    outline: none !important;
    border-color: #667eea !important;
    background-color: white !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* 终极下拉选项样式 - 覆盖所有可能的冲突 */
#crane-card-li-content select option,
#crane-TS-search-position option,
#crane-TS-search-state option,
select[name="crane-TS-search-position"] option,
select[name="crane-TS-search-state"] option {
    padding: 12px 16px !important;
    background: white !important;
    color: #374151 !important;
    border: none !important;
    border-bottom: 1px solid #f1f5f9 !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
    font-family: inherit !important;
}

#crane-card-li-content select option:hover,
#crane-TS-search-position option:hover,
#crane-TS-search-state option:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    color: #667eea !important;
    font-weight: 500 !important;
}

#crane-card-li-content select option:checked,
#crane-card-li-content select option:selected,
#crane-TS-search-position option:checked,
#crane-TS-search-position option:selected,
#crane-TS-search-state option:checked,
#crane-TS-search-state option:selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
}

/* 空选项样式 */
#crane-card-li-content select option[value=""],
#crane-TS-search-position option[value=""],
#crane-TS-search-state option[value=""] {
    color: #9ca3af !important;
    font-style: italic !important;
    background: #f9fafb !important;
}

/* 现代化选择框类样式 - 最终解决方案 */
.modern-select {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background: white url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") no-repeat right 12px center !important;
    background-size: 18px !important;
    padding: 8px 44px 8px 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    font-size: 13px !important;
    color: #374151 !important;
    cursor: pointer !important;
    box-sizing: border-box !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    font-family: inherit !important;
    line-height: normal !important;
}

.modern-select:hover {
    border-color: #667eea !important;
    background-color: #f8fafc !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

.modern-select:focus {
    outline: none !important;
    border-color: #667eea !important;
    background-color: white !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    transform: translateY(-1px) !important;
}

.modern-select option {
    padding: 12px 16px !important;
    background: white !important;
    color: #374151 !important;
    border: none !important;
    border-bottom: 1px solid #f1f5f9 !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
    font-family: inherit !important;
}

.modern-select option:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    color: #667eea !important;
    font-weight: 500 !important;
}

.modern-select option:checked,
.modern-select option:selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
}

.modern-select option[value=""] {
    color: #9ca3af !important;
    font-style: italic !important;
    background: #f9fafb !important;
}

/* 选项前的装饰性图标 */
.search-field select option:not(:first-child)::before,
#crane-TS-search-position option:not(:first-child)::before,
#crane-TS-search-state option:not(:first-child)::before {
    content: "▸" !important;
    color: #d1d5db !important;
    margin-right: 8px !important;
    transition: all 0.3s ease !important;
    font-size: 10px !important;
}

.search-field select option:hover::before,
#crane-TS-search-position option:hover::before,
#crane-TS-search-state option:hover::before {
    color: #667eea !important;
    transform: translateX(2px) !important;
}

/* 空选项（请选择...）的特殊样式 */
.search-field select option[value=""],
#crane-TS-search-position option[value=""],
#crane-TS-search-state option[value=""] {
    color: #9ca3af !important;
    font-style: italic !important;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
}

.search-field select option[value=""]:hover,
#crane-TS-search-position option[value=""]:hover,
#crane-TS-search-state option[value=""]:hover {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
    color: #6b7280 !important;
}

/* Firefox 特殊处理 - 美化版 */
@-moz-document url-prefix() {
    .search-field select option:hover,
    #crane-TS-search-position option:hover,
    #crane-TS-search-state option:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        box-shadow: inset 4px 0 0 rgba(255, 255, 255, 0.3) !important;
    }

    .search-field select option:checked,
    #crane-TS-search-position option:checked,
    #crane-TS-search-state option:checked {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        font-weight: 600 !important;
    }
}

/* Webkit 浏览器选项样式 - 美化版 */
.search-field select option:hover,
#crane-TS-search-position option:hover,
#crane-TS-search-state option:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    color: #667eea !important;
    transform: translateX(4px) !important;
    box-shadow: inset 4px 0 0 #667eea !important;
    font-weight: 500 !important;
}

.search-field select option:checked,
.search-field select option:selected,
#crane-TS-search-position option:checked,
#crane-TS-search-position option:selected,
#crane-TS-search-state option:checked,
#crane-TS-search-state option:selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.2) !important;
}

/* 选择框打开时的样式 - 美化版 */
.search-field select[size],
.search-field select[multiple] {
    border-radius: 16px;
    max-height: 240px;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(102, 126, 234, 0.1);
    border: 2px solid #667eea;
    background: white;
    animation: dropdownOpen 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 下拉动画 */
@keyframes dropdownOpen {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 选项分组样式（如果有optgroup） */
.search-field select optgroup {
    font-weight: 600;
    color: #667eea;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 8px 16px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #667eea;
}

/* 选项数量较多时的渐变遮罩效果 */
.search-field select[size]::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, rgba(255, 255, 255, 0.9));
    pointer-events: none;
}

/* 自定义滚动条样式 */
.search-field select::-webkit-scrollbar {
    width: 6px;
}

.search-field select::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.search-field select::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.search-field select::-webkit-scrollbar-thumb:hover {
    background: #5a67d8;
}

/* 选择框禁用状态 */
.search-field select:disabled {
    background-color: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
    border-color: #e5e7eb;
}

.search-field select:disabled option {
    color: #9ca3af;
}

/* 选择框验证状态 */
.search-field select.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.search-field select.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.search-buttons {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.modern-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modern-btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.modern-btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
}
body {
    /*overflow: hidden;*/
}

/* 现代化起重机卡片样式 */
.modern-crane-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-crane-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 20px 20px 0 0;
}

.modern-crane-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 15px;
    font-weight: 600;
    color: #1f2937;
}

.card-title i {
    font-size: 18px;
    color: #667eea;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.status-online {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.status-offline {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

/* 离线卡片样式 */
.modern-crane-card:has(.status-offline) {
    opacity: 0.7;
    background: linear-gradient(145deg, #f9fafb 0%, #f3f4f6 100%);
}

.modern-crane-card:has(.connection-status.offline) {
    opacity: 0.7;
}

.status-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.card-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.data-item {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(229, 231, 235, 0.5);
}

.data-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-label {
    font-size: 11px;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.data-value {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
}

.value-unit {
    font-size: 12px;
    color: #9ca3af;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-normal {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-warning {
    background: #f59e0b;
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.status-danger {
    background: #ef4444;
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.card-footer {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-ip {
    font-size: 11px;
    color: #6b7280;
    font-family: 'Courier New', monospace;
}

.update-time {
    font-size: 10px;
    color: #9ca3af;
}

/* 现代化左侧导航栏样式 */
.crane-left-nav {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 0 12px 12px 0;
    box-shadow: 2px 0 12px rgba(102, 126, 234, 0.2);
    border: none !important;
    position: relative;
    z-index: 100;
    min-width: 100px;
    width: 100% !important;
}

.crane-left-nav .layui-nav-item {
    margin: 4px 4px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
}

.crane-left-nav .layui-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.crane-left-nav .layui-nav-item:hover::before {
    left: 100%;
}

.crane-left-nav .layui-nav-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.crane-left-nav .layui-nav-item a {
    color: white !important;
    font-weight: 500;
    padding: 10px 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;
    z-index: 1;
    font-size: 11px;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    width: 100%;
    box-sizing: border-box;
}

.crane-left-nav .layui-nav-item a::before {
    content: '';
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.crane-left-nav .layui-nav-item:hover a::before {
    background: white;
    transform: scale(1.5);
}

.crane-left-nav .layui-this {
    background: rgba(255, 255, 255, 0.2) !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.crane-left-nav .layui-this a::before {
    background: white;
    transform: scale(1.2);
}

/* 现代化分页样式 */
#crane_card_page_index .layui-laypage {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 32px;
}

#crane_card_page_index .layui-laypage a,
#crane_card_page_index .layui-laypage span {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 12px 16px;
    color: #374151;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#crane_card_page_index .layui-laypage a:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

#crane_card_page_index .layui-laypage .layui-laypage-curr {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: #667eea !important;
    color: white !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 修复分页当前页的绿色覆盖层问题 */
#crane_card_page_index .layui-laypage .layui-laypage-curr::before,
#crane_card_page_index .layui-laypage .layui-laypage-curr::after {
    display: none !important;
    content: none !important;
}

/* 只隐藏绿色覆盖层的em元素，保留文字内容 */
#crane_card_page_index .layui-laypage .layui-laypage-curr > em {
    background: none !important;
    background-color: transparent !important;
    background-image: none !important;
    color: white !important;
    font-style: normal !important;
}

/* 确保当前页文字显示正常 */
#crane_card_page_index .layui-laypage .layui-laypage-curr,
#crane_card_page_index .layui-laypage .layui-laypage-curr cite,
#crane_card_page_index .layui-laypage .layui-laypage-curr em,
#crane_card_page_index .layui-laypage .layui-laypage-curr span {
    color: white !important;
    font-style: normal !important;
    position: relative !important;
    z-index: 2 !important;
}

/* 只隐藏非当前页的覆盖元素 */
#crane_card_page_index .layui-laypage span:not(.layui-laypage-curr) em {
    display: none !important;
}

#crane_card_page_index .layui-laypage span::before,
#crane_card_page_index .layui-laypage span::after {
    display: none !important;
    content: none !important;
}

/* 强制移除Layui默认的绿色背景和覆盖层 */
#crane_card_page_index .layui-laypage .layui-laypage-curr {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: 2px solid #667eea !important;
    color: white !important;
}

/* 确保当前页内容可见 */
#crane_card_page_index .layui-laypage .layui-laypage-curr > * {
    color: white !important;
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* 特别处理当前页的文字显示 */
#crane_card_page_index .layui-laypage .layui-laypage-curr {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 500 !important;
    text-align: center !important;
}

/* 确保页码数字显示 - 最高优先级 */
#crane_card_page_index .layui-laypage .layui-laypage-curr em,
#crane_card_page_index .layui-laypage .layui-laypage-curr cite {
    display: inline !important;
    color: white !important;
    font-style: normal !important;
    font-weight: 500 !important;
    background: none !important;
    background-color: transparent !important;
    background-image: none !important;
    position: static !important;
}

/* 如果页码还是不显示，直接设置文本内容 */
#crane_card_page_index .layui-laypage .layui-laypage-curr::after {
    content: attr(data-page) !important;
    color: white !important;
    font-weight: 500 !important;
    display: inline !important;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .search-row {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .search-fields-group {
        justify-content: flex-start;
    }

    .search-buttons {
        align-self: flex-end;
    }
}

@media (max-width: 768px) {
    .modern-crane-card {
        margin-bottom: 12px;
    }

    .card-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .search-row {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .search-fields-group {
        flex-direction: column;
        gap: 12px;
    }

    .search-field {
        min-width: auto;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .search-field label {
        min-width: auto;
    }

    .search-buttons {
        justify-content: center;
        align-self: center;
    }
}

/* 空数据状态样式 */
.none-span {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #9ca3af;
    font-size: 16px;
}

.none-span::before {
    content: '📊';
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Tab标题样式优化 */
.layui-tab-title {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px 16px 0 0;
    padding: 0 20px;
    border-bottom: 2px solid #e5e7eb;
}

.layui-tab-title li {
    margin-right: 8px;
    border-radius: 12px 12px 0 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.layui-tab-title li::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.layui-tab-title li.layui-this::before {
    transform: scaleX(1);
}

.layui-tab-title li a {
    padding: 16px 24px;
    font-weight: 600;
    color: #374151;
    transition: all 0.3s ease;
}

.layui-tab-title li.layui-this a {
    color: #667eea;
    background: white;
    border-radius: 12px 12px 0 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
.layui-tab-content::-webkit-scrollbar {
    width: 8px;
}

.layui-tab-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.layui-tab-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

.layui-tab-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

